#!/usr/bin/env python3
"""
快速状态检查工具
"""

import requests
import subprocess
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_backend():
    """检查后端状态"""
    try:
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            log("✅ 后端API正常运行")
            print(f"状态数据: {data}")
            return True
        else:
            log(f"❌ 后端API响应异常: {response.status_code}")
            return False
    except Exception as e:
        log(f"❌ 后端API连接失败: {e}")
        return False

def check_verification_urls():
    """检查验证链接"""
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            log(f"✅ 验证链接可用: {len(urls)}个")
            if urls:
                latest_url = urls[0]
                log("🔗 最新验证链接:")
                print(latest_url)
                return latest_url
            else:
                log("⚠️ 没有可用的验证链接")
                return None
        else:
            log(f"❌ 验证链接获取失败: {response.status_code}")
            return None
    except Exception as e:
        log(f"❌ 验证链接获取异常: {e}")
        return None

def check_migpt_container():
    """检查MiGPT容器状态"""
    try:
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", "name=xiaoai-llm", "--format", "{{.Status}}"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            status = result.stdout.strip()
            if status:
                log(f"📦 MiGPT容器状态: {status}")
                return status
            else:
                log("📦 MiGPT容器不存在")
                return "not_exists"
        else:
            log("❌ 无法检查容器状态")
            return "error"
    except Exception as e:
        log(f"❌ 容器检查失败: {e}")
        return "error"

def main():
    print("🔍 快速状态检查")
    print("="*50)
    
    # 1. 检查后端
    backend_ok = check_backend()
    
    # 2. 检查验证链接
    verification_url = check_verification_urls()
    
    # 3. 检查容器
    container_status = check_migpt_container()
    
    print("\n" + "="*50)
    print("📊 当前状态总结:")
    print(f"🚀 后端API: {'正常' if backend_ok else '异常'}")
    print(f"🔗 验证链接: {'可用' if verification_url else '不可用'}")
    print(f"📦 MiGPT容器: {container_status}")
    
    if verification_url:
        print("\n💡 下一步操作:")
        print("1. 在浏览器中打开验证链接完成验证")
        print("2. 验证完成后等待约1小时")
        print("3. 启动MiGPT容器")
        
        choice = input("\n是否立即在浏览器中打开验证链接？(y/n): ").lower().strip()
        if choice == 'y':
            import webbrowser
            webbrowser.open(verification_url)
            log("🌐 验证链接已在浏览器中打开")
    
    print("="*50)

if __name__ == "__main__":
    main()
