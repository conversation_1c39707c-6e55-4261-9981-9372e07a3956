#!/usr/bin/env python3
"""
获取最新验证链接并打开
"""

import requests
import webbrowser
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    try:
        log("🔗 获取最新验证链接...")
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            count = data.get('count', 0)
            
            log(f"📊 检测到 {count} 个验证链接")
            
            if urls:
                latest_url = urls[0]
                log("🔗 最新验证链接:")
                print(latest_url)
                
                log("🌐 正在浏览器中打开验证链接...")
                webbrowser.open(latest_url)
                log("✅ 验证链接已在浏览器中打开")
                
                log("💡 请在浏览器中完成手机号验证")
                log("⏰ 验证完成后需要等待约1小时生效")
                
            else:
                log("❌ 没有可用的验证链接")
        else:
            log(f"❌ 获取验证链接失败: {response.status_code}")
            
    except Exception as e:
        log(f"❌ 获取验证链接失败: {e}")

if __name__ == "__main__":
    main()
