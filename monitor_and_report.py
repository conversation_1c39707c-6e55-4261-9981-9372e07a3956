#!/usr/bin/env python3
"""
监控和报告脚本
定时检查验证状态并生成报告文件
"""

import subprocess
import time
import json
from datetime import datetime, timedelta

def log_to_file(message, filename="verification_monitor.log"):
    """记录日志到文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(filename, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(f"[{timestamp}] {message}")

def create_status_report(status, details=""):
    """创建状态报告文件"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "status": status,
        "details": details,
        "next_check": None
    }
    
    with open("verification_status.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

def check_verification():
    """检查验证状态"""
    try:
        log_to_file("🔍 开始检查验证状态...")
        
        # 停止现有容器
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
        
        # 启动新容器
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js",
            "-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json",
            "idootop/mi-gpt:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            log_to_file(f"❌ 容器启动失败: {result.stderr}")
            create_status_report("container_failed", result.stderr)
            return "container_failed"
        
        log_to_file("✅ 容器启动成功，等待初始化...")
        time.sleep(20)
        
        # 检查日志
        log_result = subprocess.run(
            ["docker", "logs", "--tail", "15", "xiaoai-llm"],
            capture_output=True, text=True, timeout=15
        )
        
        if log_result.returncode == 0:
            logs = log_result.stdout or ""  # 防止None值

            if "Mi Services 初始化成功" in logs:
                log_to_file("🎉 验证已生效！MiGPT启动成功")
                create_status_report("success", "验证生效，系统正常运行")

                # 更新登录状态文件
                subprocess.run(["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"],
                             capture_output=True)
                log_to_file("✅ 登录状态文件已更新")

                return "success"

            elif "触发小米账号异地登录安全验证机制" in logs:
                log_to_file("⚠️ 验证尚未生效，需要继续等待")
                create_status_report("waiting", "验证尚未生效")
                
                # 停止容器避免无限重启
                subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
                return "waiting"
                
            else:
                log_to_file("⚠️ 状态不明确")
                create_status_report("unknown", logs)
                return "unknown"
        else:
            log_to_file("❌ 无法获取容器日志")
            create_status_report("log_failed", "无法获取容器日志")
            return "log_failed"
            
    except Exception as e:
        error_msg = f"检查过程异常: {e}"
        log_to_file(f"❌ {error_msg}")
        create_status_report("error", error_msg)
        return "error"

def main():
    log_to_file("⏰ 自动验证监控程序启动")
    log_to_file("📅 检查计划: 10分钟、20分钟、30分钟、60分钟后")
    
    # 检查间隔（分钟）
    check_intervals = [10, 20, 30, 60]
    start_time = datetime.now()
    
    log_to_file(f"🕐 开始时间: {start_time.strftime('%H:%M:%S')}")
    
    for i, interval in enumerate(check_intervals):
        target_time = start_time + timedelta(minutes=interval)
        log_to_file(f"⏰ 第{i+1}次检查将在 {target_time.strftime('%H:%M:%S')} 进行")
        
        # 更新状态报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "status": "waiting_for_check",
            "details": f"等待第{i+1}次检查",
            "next_check": target_time.isoformat(),
            "check_number": i+1,
            "total_checks": len(check_intervals)
        }
        
        with open("verification_status.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 等待到检查时间
        while datetime.now() < target_time:
            remaining = target_time - datetime.now()
            minutes = int(remaining.total_seconds() / 60)
            if minutes > 0 and minutes % 5 == 0:  # 每5分钟提醒一次
                log_to_file(f"⏳ 还需等待 {minutes} 分钟进行第{i+1}次检查...")
            time.sleep(60)
        
        # 执行检查
        log_to_file(f"🔍 开始第{i+1}次检查...")
        status = check_verification()
        
        if status == "success":
            log_to_file("🎉 验证成功！监控程序完成")
            
            # 创建最终成功报告
            final_report = {
                "timestamp": datetime.now().isoformat(),
                "status": "completed_success",
                "details": "验证生效，系统启动成功",
                "check_number": i+1,
                "total_time_minutes": interval,
                "system_ready": True
            }
            
            with open("verification_status.json", "w", encoding="utf-8") as f:
                json.dump(final_report, f, ensure_ascii=False, indent=2)
            
            log_to_file("📊 最终报告已生成: verification_status.json")
            break
        else:
            log_to_file(f"⚠️ 第{i+1}次检查未成功，状态: {status}")
    
    if status != "success":
        log_to_file("⚠️ 所有自动检查完成，验证仍未生效")
        
        # 创建最终失败报告
        final_report = {
            "timestamp": datetime.now().isoformat(),
            "status": "completed_failed",
            "details": "所有自动检查完成，验证仍未生效",
            "total_checks": len(check_intervals),
            "recommendation": "建议手动运行 python check_and_start.py"
        }
        
        with open("verification_status.json", "w", encoding="utf-8") as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
    
    log_to_file("📋 监控程序结束")

if __name__ == "__main__":
    main()
