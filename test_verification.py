#!/usr/bin/env python3
"""
验证测试工具 - 验证完成后立即测试
"""

import subprocess
import time
import requests
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def start_migpt_container():
    """启动MiGPT容器"""
    log("🚀 启动MiGPT容器...")
    
    try:
        # 停止现有容器
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
        
        # 启动新容器（使用持久化登录状态）
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js"
        ]
        
        # 如果有登录状态文件，挂载它
        import os
        if os.path.exists("mi-gpt/.mi.json"):
            cmd.extend(["-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json"])
            log("✅ 使用持久化登录状态文件")
        else:
            log("⚠️ 没有找到登录状态文件，将进行首次登录")
        
        cmd.append("idootop/mi-gpt:latest")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            log("✅ MiGPT容器启动成功")
            return True
        else:
            log(f"❌ MiGPT容器启动失败: {result.stderr}")
            return False
    except Exception as e:
        log(f"❌ 容器启动异常: {e}")
        return False

def monitor_container_logs():
    """监控容器日志"""
    log("👀 监控容器启动状态...")
    
    for i in range(12):  # 监控1分钟
        time.sleep(5)
        
        try:
            result = subprocess.run(
                ["docker", "logs", "--tail", "10", "xiaoai-llm"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                logs = result.stdout
                
                if "Mi Services 初始化成功" in logs:
                    log("🎉 验证成功！MiGPT启动完成")
                    print("\n📋 容器日志:")
                    print(logs)
                    return "success"
                elif "触发小米账号异地登录安全验证机制" in logs:
                    log("🛡️ 仍需要验证，验证可能尚未生效")
                    return "need_verification"
                elif "小米账号登录失败" in logs:
                    log("❌ 登录失败")
                    return "login_failed"
                else:
                    log(f"⏳ 启动中... ({i+1}/12)")
                    continue
            else:
                log("❌ 无法获取容器日志")
                continue
                
        except Exception as e:
            log(f"❌ 监控过程出错: {e}")
            continue
    
    log("⚠️ 监控超时")
    return "timeout"

def extract_login_state():
    """提取登录状态文件"""
    log("📤 尝试提取登录状态文件...")
    
    try:
        result = subprocess.run(
            ["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            log("✅ 登录状态文件已保存，下次启动将更快")
            return True
        else:
            log("⚠️ 无法提取登录状态文件")
            return False
    except Exception as e:
        log(f"❌ 提取登录状态文件失败: {e}")
        return False

def main():
    print("🛡️ 小米账号验证测试工具")
    print("="*50)
    
    print("📱 请确保您已经在浏览器中完成了小米账号验证")
    print("验证链接: https://account.xiaomi.com/identity/authStart?...")
    print("="*50)
    
    input("✅ 验证完成后，按回车键开始测试...")
    
    # 1. 启动容器
    if not start_migpt_container():
        print("❌ 容器启动失败")
        return
    
    # 2. 监控日志
    status = monitor_container_logs()
    
    # 3. 处理结果
    print("\n" + "="*50)
    if status == "success":
        print("🎉 验证测试成功！")
        print("✅ 小米账号验证已生效")
        print("✅ MiGPT服务正常运行")
        
        # 提取登录状态
        extract_login_state()
        
        print("\n🌐 系统已完全启动，您可以:")
        print("1. 使用小爱音箱进行AI对话")
        print("2. 访问管理界面: file:///d:/XiaoaiTX/simple-frontend.html")
        print("3. 查看API状态: http://localhost:8000/api/status")
        
    elif status == "need_verification":
        print("⚠️ 验证尚未生效")
        print("💡 可能的原因:")
        print("1. 验证刚完成，需要等待几分钟")
        print("2. 小米系统需要时间同步验证状态")
        print("3. 根据官方说明，可能需要等待约1小时")
        
        print("\n🔄 建议操作:")
        print("1. 等待10-15分钟后重新运行此脚本")
        print("2. 或者等待1小时后再试")
        
    elif status == "login_failed":
        print("❌ 登录失败")
        print("💡 可能需要:")
        print("1. 重新获取验证链接")
        print("2. 重新完成验证流程")
        
    else:
        print("⚠️ 测试超时或状态不明确")
        print("💡 建议:")
        print("1. 检查容器日志: docker logs xiaoai-llm")
        print("2. 稍后重新运行此脚本")
    
    print("="*50)

if __name__ == "__main__":
    main()
