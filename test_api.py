#!/usr/bin/env python3
"""
API测试工具
"""

import requests
import json
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_api():
    """测试API连接"""
    base_url = "http://localhost:8000"
    
    # 测试根路径
    try:
        log("🔍 测试API根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        log(f"✅ 根路径响应: {response.status_code}")
        print(f"响应内容: {response.text}")
    except Exception as e:
        log(f"❌ 根路径测试失败: {e}")
        return False
    
    # 测试状态接口
    try:
        log("🔍 测试状态接口...")
        response = requests.get(f"{base_url}/api/status", timeout=5)
        log(f"✅ 状态接口响应: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"状态数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        log(f"❌ 状态接口测试失败: {e}")
    
    # 测试统计接口
    try:
        log("🔍 测试统计接口...")
        response = requests.get(f"{base_url}/api/statistics", timeout=5)
        log(f"✅ 统计接口响应: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"统计数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        log(f"❌ 统计接口测试失败: {e}")
    
    # 测试验证接口
    try:
        log("🔍 测试验证接口...")
        response = requests.get(f"{base_url}/api/verification-urls", timeout=5)
        log(f"✅ 验证接口响应: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"验证数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        log(f"❌ 验证接口测试失败: {e}")
    
    return True

def test_frontend():
    """测试前端页面"""
    try:
        log("🔍 测试前端页面...")
        with open("simple-frontend.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键元素
        if "API_BASE = 'http://localhost:8000'" in content:
            log("✅ 前端API配置正确")
        else:
            log("❌ 前端API配置可能有问题")
        
        if "testAPI" in content:
            log("✅ 前端包含API测试功能")
        else:
            log("❌ 前端缺少API测试功能")
            
    except Exception as e:
        log(f"❌ 前端页面检查失败: {e}")

if __name__ == "__main__":
    print("🚀 API和前端测试工具")
    print("="*50)
    
    test_api()
    print("\n" + "="*50)
    test_frontend()
