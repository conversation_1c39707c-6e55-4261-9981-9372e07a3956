/**
 * 智能LLM路由器
 * 根据用户输入智能选择最合适的AI模型
 */

class LLMRouter {
  constructor(config) {
    this.config = config;
    this.models = config.models || {};
    this.routingRules = config.routing || {};
    this.costControl = config.costControl || {};
    this.monitoring = config.monitoring || {};
    
    // 使用统计
    this.stats = {
      totalRequests: 0,
      modelUsage: {},
      totalCost: 0,
      dailyCost: 0,
      errors: 0
    };
    
    // 重置每日统计
    this.resetDailyStats();
  }

  /**
   * 智能路由 - 选择最合适的模型
   */
  async route(userInput, context = {}) {
    try {
      this.stats.totalRequests++;
      
      // 1. 检查成本限制
      if (!this.checkCostLimit()) {
        throw new Error('Daily cost limit exceeded');
      }
      
      // 2. 分析用户输入，确定意图
      const intent = this.analyzeIntent(userInput);
      
      // 3. 根据意图选择模型
      const selectedModel = this.selectModel(intent, context);
      
      // 4. 记录使用统计
      this.recordUsage(selectedModel);
      
      // 5. 调用选定的模型
      const response = await this.callModel(selectedModel, userInput, context);
      
      // 6. 更新成本统计
      this.updateCostStats(selectedModel, response);
      
      return {
        success: true,
        response: response.content,
        model: selectedModel,
        cost: response.cost,
        tokens: response.tokens
      };
      
    } catch (error) {
      this.stats.errors++;
      console.error('LLM Router Error:', error);
      
      // 尝试备用模型
      return await this.fallbackRoute(userInput, context, error);
    }
  }

  /**
   * 分析用户意图
   */
  analyzeIntent(userInput) {
    const input = userInput.toLowerCase();
    
    // 遍历路由规则，找到匹配的意图
    for (const [intentName, rule] of Object.entries(this.routingRules)) {
      const keywords = rule.keywords || [];
      
      // 检查是否包含关键词
      const hasKeyword = keywords.some(keyword => 
        input.includes(keyword.toLowerCase())
      );
      
      if (hasKeyword) {
        return {
          name: intentName,
          priority: rule.priority || 1,
          model: rule.model || 'default'
        };
      }
    }
    
    // 默认意图
    return {
      name: 'general',
      priority: 1,
      model: 'default'
    };
  }

  /**
   * 选择模型
   */
  selectModel(intent, context) {
    const requestedModel = intent.model;
    
    // 检查模型是否可用
    if (this.models[requestedModel]) {
      return requestedModel;
    }
    
    // 回退到默认模型
    return 'default';
  }

  /**
   * 调用指定模型
   */
  async callModel(modelName, userInput, context) {
    const modelConfig = this.models[modelName];
    if (!modelConfig) {
      throw new Error(`Model ${modelName} not configured`);
    }

    const provider = modelConfig.provider;
    
    switch (provider) {
      case 'deepseek':
        return await this.callDeepSeek(modelConfig, userInput, context);
      case 'wenxin':
        return await this.callWenxin(modelConfig, userInput, context);
      case 'qianwen':
        return await this.callQianwen(modelConfig, userInput, context);
      case 'gpt4':
        return await this.callGPT4(modelConfig, userInput, context);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * 调用DeepSeek模型
   */
  async callDeepSeek(config, userInput, context) {
    const response = await fetch(`${process.env.OPENAI_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: config.model,
        messages: this.buildMessages(userInput, context),
        max_tokens: config.maxTokens,
        temperature: config.temperature
      })
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`DeepSeek API Error: ${data.error?.message || 'Unknown error'}`);
    }

    return {
      content: data.choices[0].message.content,
      tokens: data.usage.total_tokens,
      cost: data.usage.total_tokens * config.costPerToken
    };
  }

  /**
   * 调用文心一言模型
   */
  async callWenxin(config, userInput, context) {
    // 首先获取access_token
    const tokenResponse = await fetch(`${process.env.WENXIN_BASE_URL}/oauth/2.0/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: process.env.WENXIN_API_KEY,
        client_secret: process.env.WENXIN_SECRET_KEY
      })
    });

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // 调用聊天接口
    const response = await fetch(
      `${process.env.WENXIN_BASE_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=${accessToken}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: this.buildMessages(userInput, context),
          temperature: config.temperature,
          max_output_tokens: config.maxTokens
        })
      }
    );

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Wenxin API Error: ${data.error_msg || 'Unknown error'}`);
    }

    return {
      content: data.result,
      tokens: data.usage?.total_tokens || 100, // 估算
      cost: (data.usage?.total_tokens || 100) * config.costPerToken
    };
  }

  /**
   * 构建消息格式
   */
  buildMessages(userInput, context) {
    const messages = [];
    
    // 系统消息
    if (context.systemPrompt) {
      messages.push({
        role: 'system',
        content: context.systemPrompt
      });
    }
    
    // 历史对话
    if (context.history && context.history.length > 0) {
      messages.push(...context.history);
    }
    
    // 当前用户输入
    messages.push({
      role: 'user',
      content: userInput
    });
    
    return messages;
  }

  /**
   * 备用路由
   */
  async fallbackRoute(userInput, context, originalError) {
    try {
      console.log('Using fallback model due to error:', originalError.message);
      
      const fallbackModel = this.config.fallback?.model || 'default';
      const response = await this.callModel(fallbackModel, userInput, context);
      
      return {
        success: true,
        response: response.content,
        model: fallbackModel,
        cost: response.cost,
        tokens: response.tokens,
        fallback: true,
        originalError: originalError.message
      };
      
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
      
      return {
        success: false,
        response: "抱歉，我现在遇到了一些技术问题，请稍后再试。",
        error: fallbackError.message,
        originalError: originalError.message
      };
    }
  }

  /**
   * 检查成本限制
   */
  checkCostLimit() {
    const dailyLimit = this.costControl.dailyLimit || 5.0;
    return this.stats.dailyCost < dailyLimit;
  }

  /**
   * 记录使用统计
   */
  recordUsage(modelName) {
    if (!this.stats.modelUsage[modelName]) {
      this.stats.modelUsage[modelName] = 0;
    }
    this.stats.modelUsage[modelName]++;
  }

  /**
   * 更新成本统计
   */
  updateCostStats(modelName, response) {
    const cost = response.cost || 0;
    this.stats.totalCost += cost;
    this.stats.dailyCost += cost;
    
    // 成本预警
    const warningThreshold = this.costControl.warningThreshold || 0.8;
    const dailyLimit = this.costControl.dailyLimit || 5.0;
    
    if (this.stats.dailyCost > dailyLimit * warningThreshold) {
      console.warn(`Daily cost warning: ${this.stats.dailyCost}/${dailyLimit}`);
    }
  }

  /**
   * 重置每日统计
   */
  resetDailyStats() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this.stats.dailyCost = 0;
      console.log('Daily cost stats reset');
      
      // 设置下一次重置
      setInterval(() => {
        this.stats.dailyCost = 0;
        console.log('Daily cost stats reset');
      }, 24 * 60 * 60 * 1000); // 24小时
      
    }, msUntilMidnight);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      costLimitStatus: {
        dailyLimit: this.costControl.dailyLimit || 5.0,
        dailyUsed: this.stats.dailyCost,
        remainingBudget: (this.costControl.dailyLimit || 5.0) - this.stats.dailyCost
      }
    };
  }
}

module.exports = LLMRouter;
