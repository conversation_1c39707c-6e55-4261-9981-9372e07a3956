@echo off
chcp 65001 >nul
echo ================================
echo 小爱同学AI大模型升级项目启动
echo ================================
echo.

echo [INFO] 检查配置文件...

REM 检查是否存在配置文件
if not exist "mi-gpt\.env" (
    echo [ERROR] 配置文件 mi-gpt\.env 不存在
    echo [INFO] 请先运行项目设置
    pause
    exit /b 1
)

if not exist "mi-gpt\.migpt.js" (
    echo [ERROR] 配置文件 mi-gpt\.migpt.js 不存在
    echo [INFO] 请先运行项目设置
    pause
    exit /b 1
)

echo [INFO] ✓ 配置文件检查完成

echo.
echo [INFO] 启动小爱同学AI服务...
echo [INFO] 请确保您已经：
echo [INFO] 1. 在 mi-gpt\.env 中填入了正确的API密钥
echo [INFO] 2. 在 mi-gpt\.migpt.js 中填入了小米账号信息
echo.

REM 进入项目目录并启动Docker容器
cd mi-gpt

echo [INFO] 正在启动Docker容器...
docker run -d ^
  --name xiaoai-llm ^
  --env-file .env ^
  -v "%cd%\.migpt.js:/app/.migpt.js" ^
  --restart unless-stopped ^
  idootop/mi-gpt:latest

if errorlevel 1 (
    echo [ERROR] 启动失败，请检查配置
    echo [INFO] 常见问题：
    echo [INFO] 1. API密钥是否正确
    echo [INFO] 2. 小米账号信息是否正确
    echo [INFO] 3. Docker是否正常运行
    pause
    exit /b 1
)

echo [INFO] ✓ 服务启动成功！

echo.
echo [INFO] 等待服务初始化...
timeout /t 5 /nobreak >nul

echo [INFO] 检查服务状态...
docker ps | findstr "xiaoai-llm" >nul
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    echo [INFO] 查看错误日志：
    docker logs xiaoai-llm
    pause
    exit /b 1
) else (
    echo [INFO] ✓ 服务运行正常
)

echo.
echo ================================
echo 🎉 启动完成！
echo ================================
echo.
echo 现在您可以对小爱同学说话测试：
echo.
echo 💬 "小爱同学，你好"
echo 💬 "小爱同学，今天天气怎么样？"
echo 💬 "小爱同学，帮我写一首诗"
echo 💬 "小爱同学，打开客厅的灯"
echo.
echo ================================
echo 管理命令：
echo ================================
echo 📊 查看日志：docker logs -f xiaoai-llm
echo 🔄 重启服务：docker restart xiaoai-llm
echo ⏹️  停止服务：docker stop xiaoai-llm
echo 🗑️  删除服务：docker rm -f xiaoai-llm
echo.
echo 🎯 享受您的AI升级版小爱同学吧！
echo.
pause
