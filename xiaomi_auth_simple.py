#!/usr/bin/env python3
"""
小米账号安全验证助手 - 简化版
监控MiGPT日志并自动打开验证页面
"""

import re
import time
import webbrowser
import subprocess
import sys
from datetime import datetime
from urllib.parse import unquote

class SimpleAuthHelper:
    def __init__(self):
        self.container_name = "xiaoai-llm"
        self.verification_urls = []
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_verification_urls(self, log_line):
        """从日志中提取验证URL"""
        patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)',
            r'"notificationUrl":"(https://account\.xiaomi\.com/[^"]+)"'
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, log_line)
            for match in matches:
                decoded_url = unquote(match)
                if decoded_url not in urls:
                    urls.append(decoded_url)
        
        return urls
    
    def open_verification_url(self, url):
        """在浏览器中打开验证URL"""
        try:
            self.log("🌐 检测到验证需求，正在自动打开浏览器...")
            webbrowser.open(url)
            self.log("✅ 验证页面已在浏览器中打开")
            self.log("📱 请在浏览器中完成手机号验证")
            self.log("⏰ 验证成功后需要等待约1小时才能生效")
            print("\n" + "="*60)
            print("验证链接:")
            print(url)
            print("="*60 + "\n")
            return True
        except Exception as e:
            self.log(f"❌ 打开浏览器失败: {e}")
            self.log(f"🔗 请手动复制以下链接到浏览器:")
            print(url)
            return False
    
    def check_container_exists(self):
        """检查Docker容器是否存在"""
        try:
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            return self.container_name in result.stdout
        except subprocess.CalledProcessError:
            return False
    
    def start_container_if_needed(self):
        """如果容器未运行则启动"""
        try:
            # 检查容器状态
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            
            if self.container_name not in result.stdout:
                self.log("🔄 MiGPT容器未运行，正在启动...")
                subprocess.run(["docker", "start", self.container_name], 
                              capture_output=True, check=True)
                self.log("✅ MiGPT容器已启动")
                time.sleep(3)  # 等待容器启动
            else:
                self.log("✅ MiGPT容器正在运行")
                
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 启动容器失败: {e}")
            return False
        return True
    
    def monitor_logs(self):
        """监控Docker日志"""
        try:
            self.log(f"📊 开始监控容器 {self.container_name} 的日志...")
            self.log("💡 检测到验证需求时会自动打开浏览器")
            self.log("🔍 按 Ctrl+C 停止监控")
            print()
            
            # 启动docker logs命令
            cmd = ["docker", "logs", "-f", self.container_name]
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='ignore'
            )
            
            while True:
                try:
                    line = process.stdout.readline()
                    if not line:
                        break
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 显示重要日志
                    if any(keyword in line for keyword in ["🔥", "👉", "❌", "✅", "MiGPT", "触发小米账号"]):
                        print(line)
                    
                    # 检测验证需求
                    if "触发小米账号异地登录安全验证机制" in line:
                        self.log("🚨 检测到需要安全验证！")
                    
                    # 提取并打开验证URL
                    urls = self.extract_verification_urls(line)
                    for url in urls:
                        if url not in self.verification_urls:
                            self.verification_urls.append(url)
                            self.open_verification_url(url)
                    
                    # 检测登录状态
                    if "✅" in line and ("登录成功" in line or "连接成功" in line):
                        self.log("🎉 登录成功！")
                    
                    if "❌ 小米账号登录失败" in line:
                        self.log("⚠️ 登录失败，可能需要重新验证")
                
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.log(f"❌ 读取日志时出错: {e}")
                    break
            
            process.terminate()
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 无法启动日志监控: {e}")
        except KeyboardInterrupt:
            self.log("👋 监控已停止")
    
    def run(self):
        """运行验证助手"""
        print("🚀 小米账号安全验证助手 - 简化版")
        print("="*50)
        
        # 检查Docker
        try:
            subprocess.run(["docker", "--version"], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("❌ 未检测到Docker，请先安装Docker")
            return False
        
        # 检查容器
        if not self.check_container_exists():
            self.log(f"❌ 容器 {self.container_name} 不存在")
            self.log("💡 请先运行以下命令创建容器:")
            print(f"docker run -d --name {self.container_name} --env-file .env -v \"%cd%\\.migpt.js:/app/.migpt.js\" --restart unless-stopped idootop/mi-gpt:latest")
            return False
        
        # 启动容器
        if not self.start_container_if_needed():
            return False
        
        # 开始监控
        self.monitor_logs()
        
        return True

def main():
    """主函数"""
    helper = SimpleAuthHelper()
    
    try:
        helper.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")

if __name__ == "__main__":
    main()
