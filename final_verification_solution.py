#!/usr/bin/env python3
"""
最终的小米账号验证解决方案
"""

import requests
import subprocess
import webbrowser
import time
import os
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    print("🎯 最终小米账号验证解决方案")
    print("="*50)
    
    # 1. 停止容器
    log("🛑 停止容器避免重启循环...")
    try:
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
        log("✅ 容器已停止")
    except:
        log("⚠️ 停止容器失败或容器已停止")
    
    # 2. 获取验证链接
    log("🔗 获取最新验证链接...")
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=10)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            if urls:
                latest_url = urls[0]
                log("✅ 获取到验证链接")
                
                # 3. 打开验证链接
                log("🌐 在浏览器中打开验证链接...")
                webbrowser.open(latest_url)
                
                print("\n" + "="*50)
                print("📱 请在浏览器中完成以下步骤:")
                print("1. 输入手机号码")
                print("2. 输入验证码")
                print("3. 完成安全验证")
                print("="*50)
                
                # 4. 等待用户确认
                input("✅ 验证完成后，按回车键继续...")
                
                # 5. 等待验证生效
                log("⏰ 验证已完成，等待小米系统更新...")
                log("💡 根据小米官方说明，需要等待约1小时")
                
                # 6. 提供等待期间的建议
                print("\n" + "="*50)
                print("⏰ 等待期间建议:")
                print("1. 保持网络连接稳定")
                print("2. 不要重复验证")
                print("3. 可以关闭容器避免重启")
                print("4. 1小时后重新启动系统")
                print("="*50)
                
                # 7. 询问是否立即测试
                test_now = input("\n🧪 是否立即测试验证状态？(y/n): ").lower().strip()
                
                if test_now == 'y':
                    log("🧪 开始测试验证状态...")
                    
                    # 启动容器测试
                    log("🚀 启动容器进行测试...")
                    try:
                        subprocess.run(["docker", "start", "xiaoai-llm"], capture_output=True, timeout=15)
                        log("✅ 容器已启动")
                        
                        # 等待初始化
                        log("⏰ 等待容器初始化...")
                        time.sleep(15)
                        
                        # 检查日志
                        log("📋 检查容器日志...")
                        result = subprocess.run(["docker", "logs", "--tail", "10", "xiaoai-llm"], 
                                              capture_output=True, text=True, timeout=10)
                        
                        if result.returncode == 0:
                            logs = result.stdout
                            if "Mi Services 初始化成功" in logs:
                                log("🎉 验证成功！系统正常运行")
                                print("\n✅ 小米账号验证已完成！")
                                print("🚀 系统现在可以正常使用了")
                                return True
                            elif "小米账号登录失败" in logs:
                                log("❌ 验证尚未生效，需要继续等待")
                                # 停止容器
                                subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
                                log("🛑 已停止容器，避免无限重启")
                            else:
                                log("⚠️ 容器状态不明确")
                        else:
                            log("❌ 无法获取容器日志")
                            
                    except Exception as e:
                        log(f"❌ 测试过程出错: {e}")
                
                # 8. 提供最终建议
                print("\n" + "="*50)
                print("🎯 最终建议:")
                print("1. 如果验证刚完成，请等待1小时后重试")
                print("2. 使用命令: python migpt_manager.py start")
                print("3. 检查状态: python test_api.py")
                print("4. 如果仍然失败，可能需要:")
                print("   - 更换网络环境")
                print("   - 联系小米客服")
                print("   - 尝试其他小米账号")
                print("="*50)
                
                return False
                
            else:
                log("❌ 没有可用的验证链接")
                return False
        else:
            log("❌ 无法获取验证链接")
            return False
            
    except Exception as e:
        log(f"❌ 获取验证链接失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("🎉 验证解决方案执行成功！")
    else:
        print("⚠️ 验证需要时间生效，请耐心等待")
    print("="*50)
