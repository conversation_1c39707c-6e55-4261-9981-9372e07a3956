<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 小爱同学AI管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card h3 {
            color: #1890ff;
            margin-bottom: 16px;
            font-size: 1.3rem;
        }
        
        .status {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-dot.green { background: #52c41a; }
        .status-dot.red { background: #ff4d4f; }
        .status-dot.yellow { background: #faad14; }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn.success { background: #52c41a; }
        .btn.warning { background: #faad14; }
        .btn.danger { background: #ff4d4f; }
        
        .chat-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background: #1890ff;
            color: white;
            margin-left: auto;
        }
        
        .message.assistant {
            background: white;
            border: 1px solid #d9d9d9;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .log-container {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .config-form {
            display: grid;
            gap: 16px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        
        .alert.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert.warning {
            background: #fff7e6;
            border: 1px solid #ffd591;
            color: #fa8c16;
        }
        
        .alert.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 小爱同学AI管理器</h1>
            <p>AI大模型升级管理系统 - 演示版</p>
        </div>
        
        <div class="dashboard">
            <!-- 系统状态 -->
            <div class="card">
                <h3>📊 系统状态</h3>
                <div class="status">
                    <div class="status-dot green" id="api-status-dot"></div>
                    <span id="api-status-text">后端API: 运行正常</span>
                </div>
                <div class="status">
                    <div class="status-dot yellow" id="migpt-status-dot"></div>
                    <span id="migpt-status-text">MiGPT: 检查中</span>
                </div>
                <div class="status">
                    <div class="status-dot red" id="xiaomi-status-dot"></div>
                    <span id="xiaomi-status-text">小米账号: 需要配置</span>
                </div>
                <div class="status">
                    <div class="status-dot red" id="ai-status-dot"></div>
                    <span id="ai-status-text">AI模型: 需要配置</span>
                </div>
                <button class="btn" onclick="checkStatus()">刷新状态</button>
            </div>
            
            <!-- 快速操作 -->
            <div class="card">
                <h3>⚡ 快速操作</h3>
                <button class="btn success" onclick="testAPI()">测试API连接</button>
                <button class="btn warning" onclick="openVerification()">打开验证助手</button>
                <button class="btn" onclick="restartMiGPT()">重启MiGPT</button>
                <button class="btn" onclick="viewLogs()">查看日志</button>
            </div>
            
            <!-- 使用统计 -->
            <div class="card">
                <h3>📈 使用统计</h3>
                <p>今日对话: <strong id="conversations">0</strong></p>
                <p>API调用: <strong id="apiCalls">0</strong></p>
                <p>今日成本: <strong id="dailyCost">$0.00</strong></p>
                <p>成功率: <strong id="successRate">0%</strong></p>
                <button class="btn" onclick="loadStatistics()">刷新统计</button>
            </div>
        </div>
        
        <!-- 对话界面 -->
        <div class="chat-container">
            <h3>💬 AI对话测试</h3>
            <div class="alert warning">
                <strong>提示:</strong> 请先在配置页面设置AI模型API密钥，然后测试对话功能。
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <strong>小爱同学:</strong> 你好！我是AI增强版的小爱同学，请先完成配置后开始对话。
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <select id="modelSelect">
                    <option value="deepseek">DeepSeek</option>
                    <option value="wenxin">文心一言</option>
                    <option value="qianwen">通义千问</option>
                    <option value="gpt4">GPT-4</option>
                </select>
                <button class="btn" onclick="sendMessage()">发送</button>
            </div>
        </div>
        
        <!-- 配置管理 -->
        <div class="dashboard">
            <div class="card">
                <h3>🔧 AI模型配置</h3>
                <div class="config-form">
                    <div class="form-group">
                        <label>DeepSeek API Key:</label>
                        <input type="password" id="deepseekKey" placeholder="请输入DeepSeek API Key">
                    </div>
                    <div class="form-group">
                        <label>文心一言 API Key:</label>
                        <input type="password" id="wenxinKey" placeholder="请输入文心一言 API Key">
                    </div>
                    <button class="btn" onclick="saveModelConfig()">保存配置</button>
                    <button class="btn warning" onclick="testModel()">测试连接</button>
                </div>
            </div>
            
            <div class="card">
                <h3>📱 小米账号配置</h3>
                <div class="config-form">
                    <div class="form-group">
                        <label>小米ID:</label>
                        <input type="text" id="xiaomiUserId" placeholder="请输入小米ID（纯数字）">
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="xiaomiPassword" placeholder="请输入小米账号密码">
                    </div>
                    <div class="form-group">
                        <label>设备名称:</label>
                        <input type="text" id="deviceName" placeholder="请输入小爱音箱设备名称">
                    </div>
                    <button class="btn" onclick="saveXiaomiConfig()">保存配置</button>
                    <button class="btn warning" onclick="testXiaomiConnection()">测试连接</button>
                </div>
            </div>
        </div>
        
        <!-- 实时日志 -->
        <div class="card">
            <h3>📋 实时日志</h3>
            <div class="log-container" id="logContainer">
                <div>[12:00:00] 🚀 小爱同学AI管理器已启动</div>
                <div>[12:00:01] ✅ 后端API服务运行正常</div>
                <div>[12:00:02] 🔍 正在检查MiGPT状态...</div>
                <div>[12:00:03] ⚠️ 请配置AI模型API密钥</div>
                <div>[12:00:04] ⚠️ 请配置小米账号信息</div>
            </div>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 添加日志
        function addLog(message, level = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 测试API连接
        async function testAPI() {
            try {
                addLog('🔍 正在测试API连接...');
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                addLog('✅ API连接成功: ' + data.message, 'success');
            } catch (error) {
                addLog('❌ API连接失败: ' + error.message, 'error');
            }
        }
        
        // 检查系统状态
        async function checkStatus() {
            try {
                addLog('🔍 正在检查系统状态...');
                const response = await fetch(`${API_BASE}/api/status`);
                const status = await response.json();

                // 更新MiGPT状态
                updateStatusDisplay('migpt', status.migpt_status);

                // 更新小米账号状态
                updateStatusDisplay('xiaomi', status.xiaomi_login);

                // 更新AI模型状态
                updateStatusDisplay('ai', status.ai_model);

                addLog('📊 系统状态已更新', 'success');
                console.log('系统状态:', status);
            } catch (error) {
                addLog('❌ 获取状态失败: ' + error.message, 'error');
            }
        }

        // 更新状态显示
        function updateStatusDisplay(component, status) {
            const statusDot = document.getElementById(`${component}-status-dot`);
            const statusText = document.getElementById(`${component}-status-text`);

            let dotClass = 'red';
            let displayText = '';

            if (component === 'migpt') {
                switch (status) {
                    case 'running':
                        dotClass = 'green';
                        displayText = 'MiGPT: 运行正常';
                        break;
                    case 'restarting':
                        dotClass = 'yellow';
                        displayText = 'MiGPT: 重启中';
                        break;
                    case 'stopped':
                        dotClass = 'red';
                        displayText = 'MiGPT: 已停止';
                        break;
                    case 'not_found':
                        dotClass = 'red';
                        displayText = 'MiGPT: 容器未找到';
                        break;
                    case 'error':
                        dotClass = 'red';
                        displayText = 'MiGPT: 检查失败';
                        break;
                    default:
                        dotClass = 'yellow';
                        displayText = 'MiGPT: 检查中';
                }
            } else if (component === 'xiaomi') {
                switch (status) {
                    case 'connected':
                        dotClass = 'green';
                        displayText = '小米账号: 已连接';
                        break;
                    case 'need_verification':
                        dotClass = 'yellow';
                        displayText = '小米账号: 需要验证';
                        break;
                    case 'login_failed':
                        dotClass = 'red';
                        displayText = '小米账号: 登录失败';
                        break;
                    case 'configured':
                        dotClass = 'yellow';
                        displayText = '小米账号: 已配置';
                        break;
                    default:
                        dotClass = 'red';
                        displayText = '小米账号: 需要配置';
                }
            } else if (component === 'ai') {
                switch (status) {
                    case 'connected':
                        dotClass = 'green';
                        displayText = 'AI模型: 已连接';
                        break;
                    case 'configured':
                        dotClass = 'yellow';
                        displayText = 'AI模型: 已配置';
                        break;
                    default:
                        dotClass = 'red';
                        displayText = 'AI模型: 需要配置';
                }
            }

            // 更新状态点颜色
            statusDot.className = `status-dot ${dotClass}`;

            // 更新状态文本
            statusText.textContent = displayText;
        }
        
        // 加载统计数据
        async function loadStatistics() {
            try {
                addLog('📈 正在加载使用统计...');
                const response = await fetch(`${API_BASE}/api/statistics`);
                const stats = await response.json();
                
                document.getElementById('conversations').textContent = stats.total_conversations || 0;
                document.getElementById('apiCalls').textContent = stats.total_api_calls || 0;
                document.getElementById('dailyCost').textContent = '$' + (stats.daily_cost || 0).toFixed(4);
                document.getElementById('successRate').textContent = (stats.success_rate || 0).toFixed(1) + '%';
                
                addLog('✅ 统计数据已更新', 'success');
            } catch (error) {
                addLog('❌ 加载统计失败: ' + error.message, 'error');
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const model = document.getElementById('modelSelect').value;
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            const chatMessages = document.getElementById('chatMessages');
            const userMsg = document.createElement('div');
            userMsg.className = 'message user';
            userMsg.innerHTML = `<strong>您:</strong> ${message}`;
            chatMessages.appendChild(userMsg);
            
            input.value = '';
            
            try {
                addLog(`💬 发送消息到${model}: ${message}`);
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message, model })
                });
                
                const data = await response.json();
                
                // 添加AI回复
                const aiMsg = document.createElement('div');
                aiMsg.className = 'message assistant';
                aiMsg.innerHTML = `<strong>小爱同学 (${model}):</strong> ${data.content || data.response}`;
                chatMessages.appendChild(aiMsg);
                
                chatMessages.scrollTop = chatMessages.scrollHeight;
                addLog('✅ 收到AI回复', 'success');
                
            } catch (error) {
                addLog('❌ 发送消息失败: ' + error.message, 'error');
                
                const errorMsg = document.createElement('div');
                errorMsg.className = 'message assistant';
                errorMsg.innerHTML = `<strong>系统:</strong> 发送失败，请检查配置`;
                chatMessages.appendChild(errorMsg);
            }
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 保存模型配置
        async function saveModelConfig() {
            const config = {
                deepseek: {
                    api_key: document.getElementById('deepseekKey').value,
                    enabled: true
                },
                wenxin: {
                    api_key: document.getElementById('wenxinKey').value,
                    enabled: true
                }
            };
            
            try {
                addLog('💾 正在保存AI模型配置...');
                const response = await fetch(`${API_BASE}/api/model-config`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                if (response.ok) {
                    addLog('✅ AI模型配置保存成功', 'success');
                } else {
                    addLog('❌ 保存配置失败', 'error');
                }
            } catch (error) {
                addLog('❌ 保存配置失败: ' + error.message, 'error');
            }
        }
        
        // 测试模型连接
        async function testModel() {
            try {
                addLog('🧪 正在测试AI模型连接...');
                const response = await fetch(`${API_BASE}/api/test-model`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ model: 'deepseek' })
                });
                
                const result = await response.json();
                if (result.success) {
                    addLog('✅ AI模型连接测试成功', 'success');
                } else {
                    addLog('❌ AI模型连接测试失败: ' + result.message, 'error');
                }
            } catch (error) {
                addLog('❌ 测试失败: ' + error.message, 'error');
            }
        }
        
        // 保存小米配置
        async function saveXiaomiConfig() {
            const config = {
                userId: document.getElementById('xiaomiUserId').value,
                password: document.getElementById('xiaomiPassword').value,
                did: document.getElementById('deviceName').value
            };
            
            try {
                addLog('💾 正在保存小米账号配置...');
                const response = await fetch(`${API_BASE}/api/xiaomi-config`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                if (response.ok) {
                    addLog('✅ 小米账号配置保存成功', 'success');
                } else {
                    addLog('❌ 保存配置失败', 'error');
                }
            } catch (error) {
                addLog('❌ 保存配置失败: ' + error.message, 'error');
            }
        }
        
        // 测试小米连接
        async function testXiaomiConnection() {
            try {
                addLog('🧪 正在测试小米账号连接...');
                const response = await fetch(`${API_BASE}/api/test-xiaomi-connection`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                if (result.success) {
                    addLog('✅ 小米账号连接测试成功', 'success');
                } else {
                    addLog('❌ 小米账号连接测试失败: ' + result.message, 'error');
                }
            } catch (error) {
                addLog('❌ 测试失败: ' + error.message, 'error');
            }
        }
        
        // 重启MiGPT
        async function restartMiGPT() {
            try {
                addLog('🔄 正在重启MiGPT...');
                const response = await fetch(`${API_BASE}/api/restart-migpt`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    addLog('✅ MiGPT重启成功', 'success');
                } else {
                    addLog('❌ MiGPT重启失败', 'error');
                }
            } catch (error) {
                addLog('❌ 重启失败: ' + error.message, 'error');
            }
        }
        
        // 打开验证助手
        function openVerification() {
            addLog('🛡️ 验证助手功能正在开发中...');
            alert('验证助手功能正在开发中，请使用命令行版本的验证助手。');
        }
        
        // 查看日志
        function viewLogs() {
            addLog('📋 日志查看功能已激活');
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('🧹 日志已清空');
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🎯 小爱同学AI管理器演示版已加载');
            addLog('💡 请先配置AI模型API密钥和小米账号信息');
            
            // 自动测试API连接
            setTimeout(testAPI, 1000);

            // 自动检查系统状态
            setTimeout(checkStatus, 1500);

            // 自动加载统计数据
            setTimeout(loadStatistics, 2000);

            // 定期更新状态（每30秒）
            setInterval(checkStatus, 30000);
        };
    </script>
</body>
</html>
