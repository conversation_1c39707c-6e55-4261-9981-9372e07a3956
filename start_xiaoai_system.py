#!/usr/bin/env python3
"""
小爱同学AI管理器 - 系统启动脚本
一键启动所有组件
"""

import subprocess
import time
import webbrowser
import requests
from datetime import datetime

def log(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_docker():
    """检查Docker状态"""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, check=True)
        log("✅ Docker已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        log("❌ Docker未安装或未启动")
        return False

def check_migpt_container():
    """检查MiGPT容器状态"""
    try:
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", "name=xiaoai-llm", "--format", "{{.Names}}"],
            capture_output=True, text=True, check=True
        )
        
        if "xiaoai-llm" in result.stdout:
            log("✅ MiGPT容器存在")
            return True
        else:
            log("❌ MiGPT容器不存在")
            return False
    except subprocess.CalledProcessError:
        log("❌ 无法检查容器状态")
        return False

def start_migpt_container():
    """启动MiGPT容器"""
    try:
        log("🚀 启动MiGPT容器...")
        subprocess.run(["docker", "start", "xiaoai-llm"], check=True, capture_output=True)
        log("✅ MiGPT容器启动成功")
        return True
    except subprocess.CalledProcessError:
        log("❌ MiGPT容器启动失败")
        return False

def start_backend():
    """启动后端服务"""
    try:
        log("🚀 启动后端API服务...")
        process = subprocess.Popen(
            ["python", "backend/main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务启动
        time.sleep(5)
        
        # 测试API连接
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            log("✅ 后端API服务启动成功")
            return True, process
        else:
            log("❌ 后端API服务启动失败")
            return False, None
            
    except Exception as e:
        log(f"❌ 启动后端服务失败: {e}")
        return False, None

def open_frontend():
    """打开前端界面"""
    try:
        log("🌐 打开前端管理界面...")
        webbrowser.open("file:///d:/XiaoaiTX/simple-frontend.html")
        log("✅ 前端界面已在浏览器中打开")
        return True
    except Exception as e:
        log(f"❌ 打开前端界面失败: {e}")
        return False

def check_system_status():
    """检查系统状态"""
    try:
        log("📊 检查系统状态...")
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            log("✅ 系统状态:")
            log(f"  📱 MiGPT: {status.get('migpt_status', 'unknown')}")
            log(f"  🐳 Docker: {status.get('docker_status', 'unknown')}")
            log(f"  🔐 小米登录: {status.get('xiaomi_login', 'unknown')}")
            log(f"  🤖 AI模型: {status.get('ai_model', 'unknown')}")
            return status
        else:
            log("❌ 无法获取系统状态")
            return None
    except Exception as e:
        log(f"❌ 检查系统状态失败: {e}")
        return None

def show_verification_info():
    """显示验证信息"""
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            if urls:
                log(f"🔗 检测到 {len(urls)} 个验证链接")
                log("💡 请使用智能验证助手获取最新验证链接")
                return True
        return False
    except Exception as e:
        log(f"❌ 获取验证信息失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 小爱同学AI管理器 - 系统启动")
    print("="*60)
    
    # 1. 检查Docker
    if not check_docker():
        print("\n❌ 请先安装并启动Docker")
        return False
    
    # 2. 检查MiGPT容器
    if not check_migpt_container():
        print("\n❌ 请先创建MiGPT容器")
        return False
    
    # 3. 启动MiGPT容器
    if not start_migpt_container():
        print("\n❌ MiGPT容器启动失败")
        return False
    
    # 4. 启动后端服务
    backend_success, backend_process = start_backend()
    if not backend_success:
        print("\n❌ 后端服务启动失败")
        return False
    
    # 5. 打开前端界面
    if not open_frontend():
        print("\n⚠️ 前端界面打开失败，请手动打开")
    
    # 6. 检查系统状态
    print("\n" + "="*60)
    status = check_system_status()
    
    # 7. 显示验证信息
    print("\n" + "="*60)
    show_verification_info()
    
    # 8. 显示访问信息
    print("\n" + "="*60)
    log("🎉 小爱同学AI管理器启动完成！")
    print("\n📋 访问信息:")
    print("  🌐 前端界面: 已在浏览器中打开")
    print("  🔧 后端API: http://localhost:8000")
    print("  📖 API文档: http://localhost:8000/docs")
    
    print("\n💡 下一步操作:")
    if status and status.get('xiaomi_login') == 'need_verification':
        print("  1. 🛡️ 完成小米账号验证（使用智能验证助手）")
        print("  2. 🤖 配置AI模型API密钥（如需要）")
        print("  3. 💬 测试AI对话功能")
    else:
        print("  1. 🔧 在前端界面配置系统参数")
        print("  2. 💬 测试AI对话功能")
        print("  3. 📊 查看使用统计")
    
    print("\n🛠️ 可用工具:")
    print("  📱 智能验证助手: python smart_verification_helper.py")
    print("  🧪 API测试工具: python test_api.py")
    print("  🔍 配置检查工具: python debug_config.py")
    
    print("\n⚠️ 注意事项:")
    print("  • 验证成功后需要等待约1小时才能生效")
    print("  • 请保持后端服务运行状态")
    print("  • 如遇问题请查看实时日志")
    
    print("\n" + "="*60)
    log("系统已启动，按 Ctrl+C 退出")
    
    try:
        # 保持运行
        while True:
            time.sleep(60)
            # 每分钟检查一次状态
            check_system_status()
    except KeyboardInterrupt:
        log("👋 正在关闭系统...")
        if backend_process:
            backend_process.terminate()
        log("✅ 系统已关闭")
    
    return True

if __name__ == "__main__":
    main()
