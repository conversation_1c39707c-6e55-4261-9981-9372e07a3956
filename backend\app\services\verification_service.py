"""
验证服务
"""

import asyncio
import re
import webbrowser
from typing import Optional
from loguru import logger

class VerificationService:
    """验证服务管理器"""
    
    def __init__(self, sio):
        self.sio = sio
        self.is_monitoring = False
        self.monitor_task = None
        self.container_name = "xiaoai-llm"
        self.sent_urls = set()  # 记录已发送的URL，避免重复
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("验证监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("验证监控已停止")
    
    async def _monitor_loop(self):
        """监控循环"""
        try:
            from .migpt_service import MiGPTService
            migpt_service = MiGPTService()
            
            while self.is_monitoring:
                try:
                    # 获取最新日志
                    logs = await migpt_service.get_logs(lines=50)
                    
                    # 检查验证需求
                    verification_urls = self._extract_verification_urls(logs)
                    
                    for url in verification_urls:
                        # 只发送新的URL，避免重复
                        if url not in self.sent_urls:
                            self.sent_urls.add(url)
                            await self.sio.emit('verification_needed', {
                                'url': url,
                                'message': '检测到需要安全验证'
                            })
                            logger.info(f"检测到新的验证URL: {url[:100]}...")
                        # 如果URL已发送过，则跳过
                    
                    # 检查登录状态
                    if "✅" in logs and "登录成功" in logs:
                        await self.sio.emit('verification_completed', {
                            'message': '登录成功'
                        })
                    
                    await asyncio.sleep(5)  # 每5秒检查一次
                    
                except Exception as e:
                    logger.error(f"监控过程中出错: {e}")
                    await asyncio.sleep(10)
                    
        except asyncio.CancelledError:
            logger.info("监控任务已取消")
        except Exception as e:
            logger.error(f"监控任务异常: {e}")
    
    def _extract_verification_urls(self, log_text: str) -> list:
        """从日志中提取验证URL"""
        patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)',
            r'"notificationUrl":"(https://account\.xiaomi\.com/[^"]+)"'
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, log_text)
            for match in matches:
                if match not in urls:
                    urls.append(match)
        
        return urls
