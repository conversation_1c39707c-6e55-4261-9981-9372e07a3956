#!/usr/bin/env python3
"""
MiGPT容器智能管理器
根据小米账号验证状态智能控制容器启停
"""

import subprocess
import time
import requests
from datetime import datetime

def log(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def get_container_status():
    """获取容器状态"""
    try:
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", "name=xiaoai-llm", "--format", "{{.Status}}"],
            capture_output=True, text=True, check=True
        )
        status = result.stdout.strip()
        return status
    except subprocess.CalledProcessError:
        return "unknown"

def get_xiaomi_login_status():
    """获取小米登录状态"""
    try:
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            return status.get('xiaomi_login', 'unknown')
        return 'unknown'
    except:
        return 'unknown'

def stop_container():
    """停止容器"""
    try:
        subprocess.run(["docker", "stop", "xiaoai-llm"], check=True, capture_output=True)
        log("✅ MiGPT容器已停止")
        return True
    except subprocess.CalledProcessError:
        log("❌ 停止容器失败")
        return False

def start_container():
    """启动容器"""
    try:
        subprocess.run(["docker", "start", "xiaoai-llm"], check=True, capture_output=True)
        log("✅ MiGPT容器已启动")
        return True
    except subprocess.CalledProcessError:
        log("❌ 启动容器失败")
        return False

def wait_for_verification():
    """等待验证完成"""
    log("⏳ 等待小米账号验证完成...")
    log("💡 请使用智能验证助手完成验证: python smart_verification_helper.py")
    
    while True:
        xiaomi_status = get_xiaomi_login_status()
        
        if xiaomi_status == 'connected':
            log("🎉 小米账号验证成功！")
            return True
        elif xiaomi_status in ['login_failed', 'init_failed']:
            log("❌ 小米账号登录失败，需要重新验证")
            return False
        elif xiaomi_status == 'need_verification':
            log("⚠️ 仍需要验证，请完成验证...")
        else:
            log(f"📊 当前状态: {xiaomi_status}")
        
        time.sleep(30)  # 每30秒检查一次

def monitor_container():
    """监控容器状态"""
    log("🔍 开始监控MiGPT容器...")
    
    while True:
        container_status = get_container_status()
        xiaomi_status = get_xiaomi_login_status()
        
        log(f"📊 容器状态: {container_status}")
        log(f"🔐 小米状态: {xiaomi_status}")
        
        # 如果小米账号需要验证，停止容器
        if xiaomi_status in ['need_verification', 'login_failed'] and 'Up' in container_status:
            log("⚠️ 小米账号需要验证，停止容器避免无限重启")
            stop_container()
            
            # 等待验证完成
            if wait_for_verification():
                log("🚀 验证完成，重新启动容器")
                start_container()
            
        # 如果小米账号已连接但容器未运行，启动容器
        elif xiaomi_status == 'connected' and 'Up' not in container_status:
            log("🚀 小米账号已连接，启动容器")
            start_container()
        
        # 如果容器在重启状态，检查是否需要干预
        elif 'Restarting' in container_status:
            log("⚠️ 容器在重启状态，检查是否需要干预")
            if xiaomi_status in ['need_verification', 'login_failed']:
                log("🛑 由于验证问题导致重启，停止容器")
                stop_container()
        
        time.sleep(60)  # 每分钟检查一次

def show_status():
    """显示当前状态"""
    container_status = get_container_status()
    xiaomi_status = get_xiaomi_login_status()
    
    print("📊 MiGPT容器管理器状态")
    print("="*50)
    print(f"🐳 容器状态: {container_status}")
    print(f"🔐 小米状态: {xiaomi_status}")
    
    if 'Restarting' in container_status:
        print("\n⚠️ 容器在重启循环中！")
        print("💡 建议操作:")
        print("  1. 停止容器: python migpt_manager.py stop")
        print("  2. 完成验证: python smart_verification_helper.py")
        print("  3. 启动容器: python migpt_manager.py start")
    
    elif xiaomi_status == 'need_verification':
        print("\n🛡️ 需要完成小米账号验证")
        print("💡 建议操作:")
        print("  1. 使用验证助手: python smart_verification_helper.py")
        print("  2. 或手动访问验证链接")
    
    elif xiaomi_status == 'connected' and 'Up' not in container_status:
        print("\n🚀 可以启动容器了")
        print("💡 建议操作:")
        print("  1. 启动容器: python migpt_manager.py start")

def main():
    import sys
    
    if len(sys.argv) < 2:
        print("🤖 MiGPT容器智能管理器")
        print("="*50)
        print("用法:")
        print("  python migpt_manager.py status   - 显示状态")
        print("  python migpt_manager.py stop     - 停止容器")
        print("  python migpt_manager.py start    - 启动容器")
        print("  python migpt_manager.py monitor  - 智能监控")
        print("  python migpt_manager.py auto     - 自动管理")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        show_status()
    elif command == 'stop':
        stop_container()
    elif command == 'start':
        start_container()
    elif command == 'monitor':
        monitor_container()
    elif command == 'auto':
        log("🤖 启动自动管理模式")
        try:
            monitor_container()
        except KeyboardInterrupt:
            log("👋 自动管理已停止")
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
