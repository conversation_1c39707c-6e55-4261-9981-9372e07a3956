import requests
try:
    response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
    if response.status_code == 200:
        data = response.json()
        urls = data.get('urls', [])
        if urls:
            print(f"有 {len(urls)} 个验证链接可用")
            print("最新验证链接:")
            print(urls[0])
        else:
            print("没有可用的验证链接")
    else:
        print(f"API响应异常: {response.status_code}")
except Exception as e:
    print(f"检查失败: {e}")
