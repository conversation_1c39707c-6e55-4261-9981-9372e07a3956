@echo off
chcp 65001 >nul
echo ================================
echo 小米账号安全验证助手
echo ================================
echo.

echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python 未安装，正在尝试使用系统Python...
    py --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] 未找到Python，请先安装Python
        echo 下载地址: https://www.python.org/downloads/
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo [INFO] ✓ Python 环境正常

echo.
echo 请选择验证助手版本：
echo 1) 图形界面版本（推荐）
echo 2) 命令行版本（简单）
echo 3) 手动启动MiGPT并监控
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo [INFO] 启动图形界面版本...
    %PYTHON_CMD% xiaomi_auth_helper.py
) else if "%choice%"=="2" (
    echo [INFO] 启动命令行版本...
    %PYTHON_CMD% xiaomi_auth_simple.py
) else if "%choice%"=="3" (
    goto manual_start
) else (
    echo [ERROR] 无效选择，默认使用命令行版本
    %PYTHON_CMD% xiaomi_auth_simple.py
)

goto end

:manual_start
echo.
echo ================================
echo 手动启动MiGPT并监控
echo ================================
echo.

echo [INFO] 检查Docker状态...
docker ps >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

echo [INFO] ✓ Docker 运行正常

echo [INFO] 检查MiGPT容器...
docker ps -a | findstr "xiaoai-llm" >nul
if errorlevel 1 (
    echo [INFO] 创建MiGPT容器...
    docker run -d --name xiaoai-llm --env-file .env -v "%cd%\.migpt.js:/app/.migpt.js" --restart unless-stopped idootop/mi-gpt:latest
    if errorlevel 1 (
        echo [ERROR] 创建容器失败
        pause
        exit /b 1
    )
) else (
    echo [INFO] 启动MiGPT容器...
    docker start xiaoai-llm >nul 2>&1
)

echo [INFO] ✓ MiGPT容器已启动

echo.
echo [INFO] 开始监控日志...
echo [INFO] 检测到验证需求时会自动打开浏览器
echo [INFO] 按 Ctrl+C 停止监控
echo.

REM 启动Python监控脚本
%PYTHON_CMD% xiaomi_auth_simple.py

:end
echo.
echo 验证助手已退出
pause
