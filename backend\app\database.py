"""
数据库配置和初始化
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, Boolean
from datetime import datetime
import json
import os

from .config import settings

# 创建异步引擎
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    future=True
)

# 创建异步会话
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

# 创建基类
Base = declarative_base()

# 数据库表定义
class ChatMessageDB(Base):
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, default="default")
    message = Column(Text)
    response = Column(Text)
    model = Column(String)
    cost = Column(Float, default=0.0)
    tokens = Column(Integer, default=0)
    timestamp = Column(DateTime, default=datetime.utcnow)

class ModelUsageDB(Base):
    __tablename__ = "model_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    model = Column(String)
    api_calls = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    total_cost = Column(Float, default=0.0)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    avg_response_time = Column(Float, default=0.0)
    date = Column(DateTime, default=datetime.utcnow)

class SystemLogDB(Base):
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String)
    message = Column(Text)
    source = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
    meta_data = Column(Text)  # JSON字符串

class ConfigurationDB(Base):
    __tablename__ = "configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, unique=True, index=True)
    value = Column(Text)  # JSON字符串
    updated_at = Column(DateTime, default=datetime.utcnow)

# 数据库初始化
async def init_db():
    """初始化数据库"""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")

# 获取数据库会话
async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# 配置管理
class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    async def get_config(key: str, default=None):
        """获取配置"""
        async with AsyncSessionLocal() as session:
            try:
                from sqlalchemy import select
                stmt = select(ConfigurationDB).where(ConfigurationDB.key == key)
                result = await session.execute(stmt)
                config = result.scalar_one_or_none()

                if config:
                    return json.loads(config.value)
                return default
            except Exception as e:
                print(f"获取配置失败: {e}")
                return default
    
    @staticmethod
    async def set_config(key: str, value):
        """设置配置"""
        async with AsyncSessionLocal() as session:
            try:
                config = await session.get(ConfigurationDB, key)
                if config:
                    config.value = json.dumps(value)
                    config.updated_at = datetime.utcnow()
                else:
                    config = ConfigurationDB(
                        key=key,
                        value=json.dumps(value),
                        updated_at=datetime.utcnow()
                    )
                    session.add(config)
                
                await session.commit()
                return True
            except Exception as e:
                print(f"设置配置失败: {e}")
                await session.rollback()
                return False
