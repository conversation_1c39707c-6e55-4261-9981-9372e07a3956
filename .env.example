# 小爱同学AI管理器环境配置示例
# 复制此文件为 .env 并填入您的实际配置

# ===========================================
# AI模型配置
# ===========================================

# DeepSeek API配置（推荐，性价比高）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://cn.gptapi.asia/v1

# 文心一言API配置（百度）
WENXIN_API_KEY=your_wenxin_api_key_here
WENXIN_SECRET_KEY=your_wenxin_secret_key_here

# 通义千问API配置（阿里）
QIANWEN_API_KEY=your_qianwen_api_key_here

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# ===========================================
# 小米账号配置
# ===========================================

# 小米账号信息
XIAOMI_USER_ID=your_xiaomi_user_id_here
XIAOMI_PASSWORD=your_xiaomi_password_here

# 小爱音箱设备名称或DID
XIAOMI_DEVICE_DID=your_device_name_or_did_here

# ===========================================
# 应用配置
# ===========================================

# 应用运行配置
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 安全密钥（请修改为随机字符串）
SECRET_KEY=your_secret_key_change_in_production

# ===========================================
# 数据库配置
# ===========================================

# SQLite数据库（默认）
DATABASE_URL=sqlite+aiosqlite:///./xiaoai.db

# 或使用PostgreSQL
# DATABASE_URL=postgresql+asyncpg://user:password@localhost/xiaoai

# 或使用MySQL
# DATABASE_URL=mysql+aiomysql://user:password@localhost/xiaoai

# ===========================================
# Redis配置（可选）
# ===========================================

# Redis缓存配置
REDIS_URL=redis://localhost:6379/0

# ===========================================
# Docker配置
# ===========================================

# MiGPT容器名称
DOCKER_CONTAINER_NAME=xiaoai-llm

# ===========================================
# 监控配置
# ===========================================

# 启用监控
ENABLE_MONITORING=true

# 监控间隔（秒）
MONITORING_INTERVAL=30

# ===========================================
# 成本控制配置
# ===========================================

# 每日成本限制（美元）
DAILY_COST_LIMIT=5.0

# 成本预警阈值（百分比）
COST_WARNING_THRESHOLD=0.8

# ===========================================
# 日志配置
# ===========================================

# 日志级别
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/xiaoai.log
