#!/usr/bin/env python3
"""
自动定时检查脚本
每隔一段时间自动检查验证是否生效
"""

import subprocess
import time
from datetime import datetime, timedelta

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_verification_status():
    """检查验证状态"""
    try:
        # 重启容器测试
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
        
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js",
            "-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json",
            "idootop/mi-gpt:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            return "container_failed"
        
        # 等待初始化
        time.sleep(20)
        
        # 检查日志
        log_result = subprocess.run(
            ["docker", "logs", "--tail", "10", "xiaoai-llm"],
            capture_output=True, text=True, timeout=10
        )
        
        if log_result.returncode == 0:
            logs = log_result.stdout
            
            if "Mi Services 初始化成功" in logs:
                return "success"
            elif "触发小米账号异地登录安全验证机制" in logs:
                return "need_wait"
            else:
                return "unknown"
        else:
            return "log_failed"
            
    except Exception as e:
        log(f"检查失败: {e}")
        return "error"

def main():
    print("⏰ 自动定时检查验证状态")
    print("="*50)
    
    # 设置检查间隔（分钟）
    check_intervals = [10, 20, 30, 60]  # 10分钟、20分钟、30分钟、1小时后检查
    
    start_time = datetime.now()
    log(f"开始时间: {start_time.strftime('%H:%M:%S')}")
    
    for interval in check_intervals:
        target_time = start_time + timedelta(minutes=interval)
        log(f"下次检查时间: {target_time.strftime('%H:%M:%S')} (等待{interval}分钟)")
        
        # 等待到指定时间
        while datetime.now() < target_time:
            remaining = target_time - datetime.now()
            minutes = int(remaining.total_seconds() / 60)
            if minutes > 0:
                log(f"还需等待 {minutes} 分钟...")
            time.sleep(60)  # 每分钟提醒一次
        
        # 执行检查
        log(f"🔍 第{check_intervals.index(interval)+1}次检查开始...")
        status = check_verification_status()
        
        if status == "success":
            log("🎉 验证已生效！系统启动成功")
            print("\n" + "="*50)
            print("✅ 小米账号验证通过")
            print("✅ MiGPT服务正常运行")
            print("🚀 系统已完全启动")
            print("="*50)
            break
        elif status == "need_wait":
            log("⚠️ 验证尚未生效，继续等待...")
            # 停止容器避免无限重启
            subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        else:
            log(f"❌ 检查失败: {status}")
    
    if status != "success":
        print("\n" + "="*50)
        print("⚠️ 自动检查完成，验证可能仍未生效")
        print("💡 建议手动运行: python check_and_start.py")
        print("="*50)

if __name__ == "__main__":
    main()
