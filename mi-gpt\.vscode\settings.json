{"files.eol": "\n", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "search.exclude": {"**/.git": true, "**/node_modules": true, "*.lock": true}, "files.exclude": {"**/.git": true, "**/node_modules": true}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}