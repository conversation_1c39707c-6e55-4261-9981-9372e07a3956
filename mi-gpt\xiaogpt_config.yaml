# XiaoGPT 配置文件
# 小米设备型号，可在底部标签获得
hardware: LX05

# 小米账号
account: "*********"

# 小米密码
password: "331256zL"

# 设备 DID，参考 https://github.com/yihong0618/MiService
mi_did: "LJXA"

# 使用 MI command 与小爱交互
use_command: false

# 快速停掉小爱自己的回答
mute_xiaoai: true

# 是否打印详细日志
verbose: true

# HTTP 代理地址
proxy: ""

# ===== 对话 AI 设置 =====
# 使用的 bot 类型，目前支持 chatgptapi, glm, gemini, langchain, qwen, doubao, moonshot, yi, llama
bot: chatgptapi

# 自定义 prompt
prompt: "请用100字以内回答"

# 触发词列表
keyword:
  - "请"
  - "你"
  - "傻妞"

# 更改提示词触发列表
change_prompt_keyword:
  - "更改提示词"

# 开始持续对话关键词
start_conversation: "开始持续对话"

# 结束持续对话关键词
end_conversation: "结束持续对话"

# 使用流式响应，获得更快的响应
stream: true

# 传给 Chatbot completion 接口的参数
gpt_options:
  model: "gpt-3.5-turbo"
  temperature: 0.7

# ----- OpenAI -----
# OpenAI key (使用DeepSeek API)
openai_key: "sk-b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8"

# OpenAI API 地址 (DeepSeek API)
api_base: "https://api.deepseek.com/v1"

# ===== 语音设置 =====
# 使用的 TTS 类型，目前支持 mi, edge, openai, azure, volc, baidu, google, minimax, fish
tts: mi

# TTS 参数字典
tts_options: {}
