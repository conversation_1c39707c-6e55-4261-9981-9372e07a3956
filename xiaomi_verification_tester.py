#!/usr/bin/env python3
"""
小米账号验证测试和修复工具
彻底解决验证问题
"""

import requests
import subprocess
import json
import time
import webbrowser
import os
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_command(cmd, timeout=30):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_config_file():
    """检查配置文件"""
    log("🔧 检查配置文件...")
    
    config_path = "mi-gpt\\.migpt.js"
    if not os.path.exists(config_path):
        log("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键配置
        checks = [
            ('userId: "186339069"', "用户ID"),
            ('did: "LJXA"', "设备ID"),
            ('password:', "密码")
        ]
        
        all_good = True
        for check, name in checks:
            if check in content:
                log(f"✅ {name} 配置正确")
            else:
                log(f"❌ {name} 配置异常")
                all_good = False
        
        return all_good
        
    except Exception as e:
        log(f"❌ 读取配置文件失败: {e}")
        return False

def test_verification_manually():
    """手动测试验证流程"""
    log("🧪 开始手动验证测试...")
    
    # 1. 获取验证链接
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=10)
        if response.status_code != 200:
            log("❌ 无法获取验证链接")
            return False
        
        data = response.json()
        urls = data.get('urls', [])
        if not urls:
            log("❌ 没有可用的验证链接")
            return False
        
        latest_url = urls[0]
        log(f"✅ 获取到验证链接: {latest_url[:100]}...")
        
        # 2. 测试链接可访问性
        try:
            test_response = requests.head(latest_url, timeout=15, allow_redirects=True)
            if test_response.status_code == 200:
                log("✅ 验证链接可访问")
            else:
                log(f"⚠️ 验证链接响应异常: {test_response.status_code}")
        except Exception as e:
            log(f"❌ 验证链接测试失败: {e}")
            return False
        
        # 3. 打开验证链接
        log("🌐 在浏览器中打开验证链接...")
        webbrowser.open(latest_url)
        
        # 4. 等待用户完成验证
        log("⏰ 请在浏览器中完成手机号验证...")
        log("💡 验证完成后，按回车键继续...")
        input()
        
        return True
        
    except Exception as e:
        log(f"❌ 验证测试失败: {e}")
        return False

def test_container_with_verification():
    """测试容器验证状态"""
    log("🐳 测试容器验证状态...")
    
    # 1. 启动容器
    log("🚀 启动容器...")
    success, stdout, stderr = run_command("docker start xiaoai-llm", 15)
    if not success:
        log(f"❌ 启动容器失败: {stderr}")
        return False
    
    log("✅ 容器已启动")
    
    # 2. 等待容器初始化
    log("⏰ 等待容器初始化...")
    time.sleep(10)
    
    # 3. 检查容器日志
    log("📋 检查容器日志...")
    success, stdout, stderr = run_command("docker logs --tail 20 xiaoai-llm", 15)
    
    if success and stdout:
        log("📋 容器日志内容:")
        print(stdout[-1000:])  # 显示最后1000字符
        
        # 分析日志内容
        if "Mi Services 初始化成功" in stdout:
            log("🎉 验证成功！Mi Services 初始化成功")
            return True
        elif "小米账号登录失败" in stdout:
            log("❌ 小米账号登录仍然失败")
            return False
        elif "触发小米账号异地登录安全验证机制" in stdout:
            log("🛡️ 仍需要验证，可能验证未生效")
            return False
        else:
            log("⚠️ 日志状态不明确")
            return False
    else:
        log("❌ 无法获取容器日志")
        return False

def create_verification_bypass():
    """创建验证绕过方案"""
    log("🔧 尝试创建验证绕过方案...")
    
    # 这里可以尝试一些高级的验证绕过技术
    # 比如修改配置、使用不同的登录方式等
    
    # 方案1: 尝试使用token登录（如果有的话）
    log("💡 方案1: 检查是否有可用的token...")
    
    # 方案2: 尝试修改配置使用不同的验证方式
    log("💡 方案2: 尝试修改验证配置...")
    
    # 方案3: 清除缓存重新验证
    log("💡 方案3: 清除验证缓存...")
    
    return False

def comprehensive_verification_test():
    """综合验证测试"""
    log("🎯 开始综合验证测试...")
    print("="*60)
    
    # 1. 检查配置
    config_ok = check_config_file()
    if not config_ok:
        log("❌ 配置文件有问题，请先修复配置")
        return False
    
    print()
    
    # 2. 手动验证测试
    verification_ok = test_verification_manually()
    if not verification_ok:
        log("❌ 手动验证失败")
        return False
    
    print()
    
    # 3. 容器验证测试
    container_ok = test_container_with_verification()
    if container_ok:
        log("🎉 验证成功！容器正常运行")
        return True
    
    print()
    
    # 4. 如果还是失败，尝试绕过方案
    log("⚠️ 标准验证失败，尝试高级解决方案...")
    bypass_ok = create_verification_bypass()
    
    if not bypass_ok:
        log("❌ 所有验证方案都失败了")
        print("\n" + "="*60)
        log("🆘 建议的解决方案:")
        print("1. 等待更长时间（小米验证可能需要2-3小时）")
        print("2. 尝试使用不同的网络环境")
        print("3. 检查小米账号是否被限制")
        print("4. 尝试使用不同的小米账号")
        print("5. 联系小米客服解除账号限制")
        return False
    
    return True

def main():
    print("🔧 小米账号验证测试和修复工具")
    print("="*60)
    
    # 检查后端服务
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        if response.status_code == 200:
            log("✅ 后端服务正常")
        else:
            log("❌ 后端服务异常")
            return
    except:
        log("❌ 后端服务未启动，请先启动后端服务")
        return
    
    # 开始综合测试
    success = comprehensive_verification_test()
    
    print("\n" + "="*60)
    if success:
        log("🎉 验证测试成功！系统应该可以正常工作了")
        log("🚀 建议运行: python test_api.py 确认状态")
    else:
        log("❌ 验证测试失败，需要进一步排查")
        log("📞 建议联系技术支持或查看详细日志")

if __name__ == "__main__":
    main()
