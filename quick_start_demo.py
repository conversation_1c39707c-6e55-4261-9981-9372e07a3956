#!/usr/bin/env python3
"""
小爱同学AI大模型接入快速演示
这是一个简化的演示版本，展示核心功能的实现思路
"""

import asyncio
import json
import re
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum
import time

# 模拟配置
CONFIG = {
    "wenxin": {
        "api_key": "your_wenxin_api_key",
        "secret_key": "your_wenxin_secret_key"
    },
    "max_context": 10,
    "response_timeout": 30
}

class IntentType(Enum):
    """意图类型"""
    SIMPLE_QA = "simple_qa"
    COMPLEX_DIALOGUE = "complex"
    IOT_CONTROL = "iot_control"
    CREATIVE = "creative"

@dataclass
class Message:
    """消息数据结构"""
    role: str  # user, assistant, system
    content: str
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class IntentRouter:
    """意图路由器 - 决定使用传统NLU还是大模型"""
    
    def __init__(self):
        # 简单问答模式（使用传统方法）
        self.simple_patterns = [
            r"今天天气|天气怎么样",
            r"现在几点|什么时间",
            r"设置闹钟|定个闹钟",
            r"播放音乐|放首歌"
        ]
        
        # IoT控制模式
        self.iot_patterns = [
            r"打开|关闭.*(灯|空调|电视)",
            r"调节.*温度",
            r"启动.*模式"
        ]
        
        # 创意生成关键词
        self.creative_keywords = ["写", "创作", "编", "想象", "设计", "帮我想"]
    
    def route(self, user_input: str) -> IntentType:
        """路由用户输入到合适的处理方式"""
        
        # 检查简单问答
        for pattern in self.simple_patterns:
            if re.search(pattern, user_input):
                return IntentType.SIMPLE_QA
        
        # 检查IoT控制
        for pattern in self.iot_patterns:
            if re.search(pattern, user_input):
                return IntentType.IOT_CONTROL
        
        # 检查创意生成
        if any(keyword in user_input for keyword in self.creative_keywords):
            return IntentType.CREATIVE
        
        # 默认使用复杂对话（大模型）
        return IntentType.COMPLEX_DIALOGUE

class MockLLMProvider:
    """模拟大模型提供商（实际使用时替换为真实API）"""
    
    def __init__(self, provider_name: str = "wenxin"):
        self.provider_name = provider_name
        self.call_count = 0
    
    async def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict:
        """模拟聊天补全API调用"""
        self.call_count += 1
        
        # 模拟API延迟
        await asyncio.sleep(0.5)
        
        # 获取最后一条用户消息
        user_message = ""
        for msg in reversed(messages):
            if msg["role"] == "user":
                user_message = msg["content"]
                break
        
        # 简单的模拟响应逻辑
        if "天气" in user_message:
            response = "今天天气晴朗，温度25度，适合出行。"
        elif "你好" in user_message:
            response = "你好！我是小爱同学，很高兴为您服务！有什么可以帮助您的吗？"
        elif "写" in user_message or "创作" in user_message:
            response = "我来帮您创作！请告诉我更具体的要求，比如写什么类型的内容？"
        else:
            response = f"我理解您说的是：{user_message}。这是一个很有趣的问题，让我为您详细解答..."
        
        return {
            "result": response,
            "usage": {
                "prompt_tokens": len(str(messages)),
                "completion_tokens": len(response),
                "total_tokens": len(str(messages)) + len(response)
            }
        }

class DialogueContext:
    """对话上下文管理"""
    
    def __init__(self, user_id: str, max_history: int = 10):
        self.user_id = user_id
        self.max_history = max_history
        self.messages: List[Message] = []
        self.user_profile = {}
    
    def add_message(self, role: str, content: str):
        """添加消息到上下文"""
        message = Message(role=role, content=content)
        self.messages.append(message)
        
        # 保持历史长度限制
        if len(self.messages) > self.max_history * 2:
            self.messages = self.messages[-self.max_history * 2:]
    
    def get_llm_messages(self) -> List[Dict[str, str]]:
        """获取适合大模型的消息格式"""
        llm_messages = []
        
        # 系统提示
        system_prompt = """你是小爱同学，小米公司开发的智能助手。你的特点是：
1. 友好、热情、乐于助人
2. 了解小米生态产品和服务
3. 能够控制智能家居设备
4. 回答简洁明了，适合语音交互
5. 保护用户隐私"""
        
        llm_messages.append({"role": "system", "content": system_prompt})
        
        # 历史对话
        for msg in self.messages[-8:]:  # 最近8条消息
            llm_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        return llm_messages

class TraditionalNLU:
    """传统NLU处理器（模拟现有小爱同学功能）"""
    
    def __init__(self):
        self.responses = {
            "天气": "今天天气晴朗，温度25度。",
            "时间": f"现在是{time.strftime('%H:%M')}。",
            "音乐": "正在为您播放音乐...",
            "闹钟": "闹钟已设置成功。"
        }
    
    def process(self, user_input: str) -> str:
        """处理简单意图"""
        for keyword, response in self.responses.items():
            if keyword in user_input:
                return response
        return "抱歉，我没有理解您的意思。"

class IoTController:
    """IoT设备控制器（模拟）"""
    
    def __init__(self):
        self.devices = {
            "客厅灯": {"status": "off", "type": "light"},
            "空调": {"status": "off", "temperature": 26},
            "电视": {"status": "off", "channel": 1}
        }
    
    def control_device(self, user_input: str) -> str:
        """控制IoT设备"""
        if "打开" in user_input and "灯" in user_input:
            self.devices["客厅灯"]["status"] = "on"
            return "客厅灯已打开。"
        elif "关闭" in user_input and "灯" in user_input:
            self.devices["客厅灯"]["status"] = "off"
            return "客厅灯已关闭。"
        elif "空调" in user_input:
            if "打开" in user_input:
                self.devices["空调"]["status"] = "on"
                return "空调已打开。"
            elif "关闭" in user_input:
                self.devices["空调"]["status"] = "off"
                return "空调已关闭。"
        
        return "抱歉，我无法控制该设备。"

class XiaoaiLLMService:
    """小爱同学大模型服务主类"""
    
    def __init__(self):
        self.router = IntentRouter()
        self.llm_provider = MockLLMProvider()
        self.traditional_nlu = TraditionalNLU()
        self.iot_controller = IoTController()
        self.contexts = {}  # user_id -> DialogueContext
        
        print("🤖 小爱同学AI大模型服务已启动！")
    
    def get_context(self, user_id: str) -> DialogueContext:
        """获取或创建用户对话上下文"""
        if user_id not in self.contexts:
            self.contexts[user_id] = DialogueContext(user_id)
        return self.contexts[user_id]
    
    async def process_user_input(self, user_id: str, user_input: str) -> Dict:
        """处理用户输入的主入口"""
        print(f"\n👤 用户({user_id}): {user_input}")
        
        # 获取对话上下文
        context = self.get_context(user_id)
        context.add_message("user", user_input)
        
        # 意图路由
        intent = self.router.route(user_input)
        print(f"🎯 意图识别: {intent.value}")
        
        # 根据意图选择处理方式
        if intent == IntentType.SIMPLE_QA:
            response = self.traditional_nlu.process(user_input)
            method = "传统NLU"
        
        elif intent == IntentType.IOT_CONTROL:
            response = self.iot_controller.control_device(user_input)
            method = "IoT控制"
        
        else:  # 复杂对话或创意生成，使用大模型
            llm_messages = context.get_llm_messages()
            llm_response = await self.llm_provider.chat_completion(llm_messages)
            response = llm_response["result"]
            method = f"大模型({self.llm_provider.provider_name})"
        
        # 添加助手回复到上下文
        context.add_message("assistant", response)
        
        print(f"🤖 小爱同学({method}): {response}")
        
        return {
            "success": True,
            "response": response,
            "method": method,
            "intent": intent.value,
            "context_length": len(context.messages)
        }

async def interactive_demo():
    """交互式演示"""
    service = XiaoaiLLMService()
    user_id = "demo_user"
    
    print("\n" + "="*50)
    print("🎉 小爱同学AI大模型接入演示")
    print("="*50)
    print("输入 'quit' 退出演示")
    print("输入 'stats' 查看统计信息")
    print("="*50)
    
    while True:
        try:
            user_input = input("\n请说话: ").strip()
            
            if user_input.lower() == 'quit':
                print("👋 再见！")
                break
            
            if user_input.lower() == 'stats':
                context = service.get_context(user_id)
                print(f"📊 统计信息:")
                print(f"   - 对话轮数: {len(context.messages) // 2}")
                print(f"   - 大模型调用次数: {service.llm_provider.call_count}")
                print(f"   - 上下文长度: {len(context.messages)}")
                continue
            
            if not user_input:
                continue
            
            # 处理用户输入
            result = await service.process_user_input(user_id, user_input)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def batch_test():
    """批量测试不同类型的输入"""
    test_cases = [
        "你好",
        "今天天气怎么样？",
        "打开客厅的灯",
        "帮我写一首关于春天的诗",
        "小米手机有什么新功能？",
        "设置明天早上7点的闹钟",
        "关闭空调",
        "你能帮我想一个创意的生日礼物吗？"
    ]
    
    async def run_tests():
        service = XiaoaiLLMService()
        user_id = "test_user"
        
        print("\n" + "="*50)
        print("🧪 批量测试开始")
        print("="*50)
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}/{len(test_cases)} ---")
            result = await service.process_user_input(user_id, test_input)
            await asyncio.sleep(0.5)  # 避免请求过快
        
        print("\n✅ 批量测试完成！")
    
    asyncio.run(run_tests())

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        batch_test()
    else:
        asyncio.run(interactive_demo())
