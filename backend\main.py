#!/usr/bin/env python3
"""
小爱同学AI管理器后端服务
提供API接口和WebSocket连接，管理MiGPT服务和AI模型配置
"""

import asyncio
import json
import os
from contextlib import asynccontextmanager
from typing import Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import socketio
from loguru import logger

from app.config import settings
from app.models import *
from app.services.migpt_service import MiGPTService
from app.services.verification_service import VerificationService
from app.services.model_service import ModelService
from app.services.xiaomi_service import XiaomiService
from app.database import init_db
from app.utils.logger import setup_logger

# 设置日志
setup_logger()

# 创建Socket.IO服务器
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    async_mode="asgi"
)

# 全局服务实例
migpt_service = None
verification_service = None
model_service = None
xiaomi_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global migpt_service, verification_service, model_service, xiaomi_service
    
    logger.info("🚀 启动小爱同学AI管理器后端服务")
    
    # 初始化数据库
    await init_db()
    
    # 初始化服务
    migpt_service = MiGPTService()
    verification_service = VerificationService(sio)
    model_service = ModelService()
    xiaomi_service = XiaomiService()
    
    # 启动后台任务
    asyncio.create_task(verification_service.start_monitoring())
    
    logger.info("✅ 后端服务启动完成")
    
    yield
    
    # 清理资源
    logger.info("🛑 关闭后端服务")
    if verification_service:
        await verification_service.stop_monitoring()

# 创建FastAPI应用
app = FastAPI(
    title="小爱同学AI管理器",
    description="小爱同学AI大模型升级管理系统",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 集成Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Socket.IO事件处理
@sio.event
async def connect(sid, environ):
    """客户端连接"""
    logger.info(f"客户端连接: {sid}")
    await sio.emit('connection_status', 'connected', room=sid)

@sio.event
async def disconnect(sid):
    """客户端断开连接"""
    logger.info(f"客户端断开连接: {sid}")

@sio.event
async def send_message(sid, data):
    """处理聊天消息"""
    try:
        message = data.get('message', '')
        model = data.get('model', 'deepseek')
        
        logger.info(f"收到消息: {message} (模型: {model})")
        
        # 调用AI模型
        response = await model_service.chat_completion(message, model)
        
        # 发送响应
        await sio.emit('chat_response', {
            'response': response['content'],
            'model': response['model'],
            'cost': response.get('cost', 0),
            'tokens': response.get('tokens', 0)
        }, room=sid)
        
    except Exception as e:
        logger.error(f"处理消息失败: {e}")
        await sio.emit('error', {'message': str(e)}, room=sid)

# API路由

@app.get("/")
async def root():
    """根路径"""
    return {"message": "小爱同学AI管理器后端服务"}

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    try:
        status = {
            "migpt_status": await migpt_service.get_status(),
            "docker_status": await migpt_service.get_docker_status(),
            "xiaomi_login": await xiaomi_service.get_login_status(),
            "ai_model": await model_service.get_model_status()
        }
        return status
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/statistics")
async def get_statistics():
    """获取使用统计"""
    try:
        stats = await model_service.get_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/verification-urls")
async def get_verification_urls():
    """获取验证URL列表"""
    try:
        urls = list(verification_service.sent_urls)
        return {
            "urls": urls,
            "count": len(urls),
            "message": "验证URL列表"
        }
    except Exception as e:
        logger.error(f"获取验证URL失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/usage-history")
async def get_usage_history():
    """获取使用历史"""
    try:
        history = await model_service.get_usage_history()
        return history
    except Exception as e:
        logger.error(f"获取使用历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/model-usage")
async def get_model_usage():
    """获取模型使用分布"""
    try:
        usage = await model_service.get_model_usage()
        return usage
    except Exception as e:
        logger.error(f"获取模型使用分布失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/restart-migpt")
async def restart_migpt():
    """重启MiGPT服务"""
    try:
        result = await migpt_service.restart()
        return {"success": True, "message": "MiGPT重启成功"}
    except Exception as e:
        logger.error(f"重启MiGPT失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/chat-history")
async def get_chat_history():
    """获取聊天历史"""
    try:
        history = await model_service.get_chat_history()
        return history
    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """聊天接口"""
    try:
        response = await model_service.chat_completion(
            request.message, 
            request.model
        )
        return response
    except Exception as e:
        logger.error(f"聊天失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-monitoring")
async def start_monitoring():
    """开始监控"""
    try:
        await verification_service.start_monitoring()
        return {"success": True, "message": "监控已启动"}
    except Exception as e:
        logger.error(f"启动监控失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop-monitoring")
async def stop_monitoring():
    """停止监控"""
    try:
        await verification_service.stop_monitoring()
        return {"success": True, "message": "监控已停止"}
    except Exception as e:
        logger.error(f"停止监控失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/model-config")
async def get_model_config():
    """获取模型配置"""
    try:
        config = await model_service.get_config()
        return config
    except Exception as e:
        logger.error(f"获取模型配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/model-config")
async def save_model_config(config: ModelConfigRequest):
    """保存模型配置"""
    try:
        await model_service.save_config(config.dict())
        return {"success": True, "message": "配置保存成功"}
    except Exception as e:
        logger.error(f"保存模型配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/test-model")
async def test_model(request: TestModelRequest):
    """测试模型连接"""
    try:
        result = await model_service.test_model(request.model)
        return result
    except Exception as e:
        logger.error(f"测试模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/xiaomi-config")
async def get_xiaomi_config():
    """获取小米配置"""
    try:
        config = await xiaomi_service.get_config()
        return config
    except Exception as e:
        logger.error(f"获取小米配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/xiaomi-config")
async def save_xiaomi_config(config: XiaomiConfigRequest):
    """保存小米配置"""
    try:
        await xiaomi_service.save_config(config.dict())
        return {"success": True, "message": "配置保存成功"}
    except Exception as e:
        logger.error(f"保存小米配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/xiaomi-devices")
async def get_xiaomi_devices():
    """获取小米设备列表"""
    try:
        devices = await xiaomi_service.get_devices()
        return devices
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/test-xiaomi-connection")
async def test_xiaomi_connection():
    """测试小米连接"""
    try:
        result = await xiaomi_service.test_connection()
        return result
    except Exception as e:
        logger.error(f"测试小米连接失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 静态文件服务（用于前端）
if os.path.exists("../xiaoai-app/build"):
    app.mount("/", StaticFiles(directory="../xiaoai-app/build", html=True), name="static")

if __name__ == "__main__":
    uvicorn.run(
        "main:socket_app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
