import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, Alert, Button, Space, Tag } from 'antd';
import {
  RobotOutlined,
  ApiOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import axios from 'axios';

const Dashboard = ({ socket }) => {
  const [systemStatus, setSystemStatus] = useState({
    migpt_status: 'unknown',
    docker_status: 'unknown',
    xiaomi_login: 'unknown',
    ai_model: 'unknown'
  });

  const [statistics, setStatistics] = useState({
    total_conversations: 0,
    total_api_calls: 0,
    daily_cost: 0,
    success_rate: 0,
    avg_response_time: 0
  });

  const [usageData, setUsageData] = useState([]);
  const [modelUsage, setModelUsage] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // 每30秒刷新
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (socket) {
      socket.on('status_update', (data) => {
        setSystemStatus(prev => ({ ...prev, ...data }));
      });

      socket.on('statistics_update', (data) => {
        setStatistics(prev => ({ ...prev, ...data }));
      });

      return () => {
        socket.off('status_update');
        socket.off('statistics_update');
      };
    }
  }, [socket]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statusRes, statsRes, usageRes, modelRes] = await Promise.all([
        axios.get('/api/status'),
        axios.get('/api/statistics'),
        axios.get('/api/usage-history'),
        axios.get('/api/model-usage')
      ]);

      setSystemStatus(statusRes.data);
      setStatistics(statsRes.data);
      setUsageData(usageRes.data);
      setModelUsage(modelRes.data);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': case 'connected': case 'success': return '#52c41a';
      case 'warning': case 'pending': return '#faad14';
      case 'error': case 'failed': case 'disconnected': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'running': return '运行中';
      case 'connected': return '已连接';
      case 'success': return '正常';
      case 'warning': return '警告';
      case 'pending': return '等待中';
      case 'error': return '错误';
      case 'failed': return '失败';
      case 'disconnected': return '未连接';
      default: return '未知';
    }
  };

  const restartMiGPT = async () => {
    try {
      await axios.post('/api/restart-migpt');
      fetchDashboardData();
    } catch (error) {
      console.error('重启MiGPT失败:', error);
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  return (
    <div className="dashboard fade-in">
      <Row gutter={[24, 24]}>
        {/* 系统状态卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card status-card">
            <div className="status-icon">
              <RobotOutlined style={{ color: getStatusColor(systemStatus.migpt_status) }} />
            </div>
            <div className="status-title">MiGPT服务</div>
            <Tag color={getStatusColor(systemStatus.migpt_status)}>
              {getStatusText(systemStatus.migpt_status)}
            </Tag>
            <div className="status-description">
              小爱同学AI服务状态
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card status-card">
            <div className="status-icon">
              <ApiOutlined style={{ color: getStatusColor(systemStatus.ai_model) }} />
            </div>
            <div className="status-title">AI模型</div>
            <Tag color={getStatusColor(systemStatus.ai_model)}>
              {getStatusText(systemStatus.ai_model)}
            </Tag>
            <div className="status-description">
              大语言模型连接状态
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card status-card">
            <div className="status-icon">
              <CheckCircleOutlined style={{ color: getStatusColor(systemStatus.xiaomi_login) }} />
            </div>
            <div className="status-title">小米账号</div>
            <Tag color={getStatusColor(systemStatus.xiaomi_login)}>
              {getStatusText(systemStatus.xiaomi_login)}
            </Tag>
            <div className="status-description">
              小米账号登录状态
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="dashboard-card status-card">
            <div className="status-icon">
              <ExclamationCircleOutlined style={{ color: getStatusColor(systemStatus.docker_status) }} />
            </div>
            <div className="status-title">Docker</div>
            <Tag color={getStatusColor(systemStatus.docker_status)}>
              {getStatusText(systemStatus.docker_status)}
            </Tag>
            <div className="status-description">
              Docker容器状态
            </div>
          </Card>
        </Col>

        {/* 统计数据 */}
        <Col xs={24} lg={12}>
          <Card title="使用统计" className="dashboard-card" loading={loading}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="今日对话"
                  value={statistics.total_conversations}
                  prefix={<RobotOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="API调用"
                  value={statistics.total_api_calls}
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="今日成本"
                  value={statistics.daily_cost}
                  precision={4}
                  prefix={<DollarOutlined />}
                  suffix="USD"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均响应时间"
                  value={statistics.avg_response_time}
                  precision={1}
                  prefix={<ClockCircleOutlined />}
                  suffix="秒"
                />
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <div>成功率</div>
              <Progress 
                percent={statistics.success_rate} 
                status={statistics.success_rate > 90 ? 'success' : 'normal'}
                strokeColor={statistics.success_rate > 90 ? '#52c41a' : '#1890ff'}
              />
            </div>
          </Card>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={12}>
          <Card title="快速操作" className="dashboard-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="系统运行正常"
                description="所有服务都在正常运行中，您可以开始使用小爱同学AI功能。"
                type="success"
                showIcon
              />
              
              <Space wrap>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={restartMiGPT}
                >
                  重启MiGPT
                </Button>
                <Button 
                  onClick={() => window.location.hash = '/chat'}
                >
                  开始对话
                </Button>
                <Button 
                  onClick={() => window.location.hash = '/verification'}
                >
                  验证助手
                </Button>
                <Button 
                  onClick={() => window.location.hash = '/config/models'}
                >
                  模型配置
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 使用趋势图 */}
        <Col xs={24} lg={16}>
          <Card title="使用趋势" className="dashboard-card" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={usageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="conversations" 
                  stroke="#8884d8" 
                  name="对话数量"
                />
                <Line 
                  type="monotone" 
                  dataKey="api_calls" 
                  stroke="#82ca9d" 
                  name="API调用"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 模型使用分布 */}
        <Col xs={24} lg={8}>
          <Card title="模型使用分布" className="dashboard-card" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={modelUsage}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {modelUsage.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
