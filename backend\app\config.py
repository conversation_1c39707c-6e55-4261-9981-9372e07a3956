"""
应用配置管理
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    app_name: str = "小爱同学AI管理器"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    database_url: str = Field(
        default="sqlite+aiosqlite:///./xiaoai.db",
        env="DATABASE_URL"
    )
    
    # Redis配置
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        env="REDIS_URL"
    )
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/xiaoai.log"
    
    # 安全配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    
    # AI模型配置
    deepseek_api_key: Optional[str] = Field(None, env="DEEPSEEK_API_KEY")
    deepseek_base_url: str = Field(
        default="https://cn.gptapi.asia/v1",
        env="DEEPSEEK_BASE_URL"
    )
    
    wenxin_api_key: Optional[str] = Field(None, env="WENXIN_API_KEY")
    wenxin_secret_key: Optional[str] = Field(None, env="WENXIN_SECRET_KEY")
    
    qianwen_api_key: Optional[str] = Field(None, env="QIANWEN_API_KEY")
    
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_base_url: str = Field(
        default="https://api.openai.com/v1",
        env="OPENAI_BASE_URL"
    )
    
    # 小米配置
    xiaomi_user_id: Optional[str] = Field(None, env="XIAOMI_USER_ID")
    xiaomi_password: Optional[str] = Field(None, env="XIAOMI_PASSWORD")
    xiaomi_device_did: Optional[str] = Field(None, env="XIAOMI_DEVICE_DID")
    
    # Docker配置
    docker_container_name: str = "xiaoai-llm"
    
    # 监控配置
    enable_monitoring: bool = True
    monitoring_interval: int = 30  # 秒
    
    # 成本控制
    daily_cost_limit: float = 5.0
    cost_warning_threshold: float = 0.8
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 全局配置实例
settings = Settings()

# 模型默认配置
DEFAULT_MODEL_CONFIG = {
    "deepseek": {
        "api_key": settings.deepseek_api_key or "",
        "model": "deepseek-chat",
        "base_url": settings.deepseek_base_url,
        "max_tokens": 1000,
        "temperature": 0.7,
        "enabled": True
    },
    "wenxin": {
        "api_key": settings.wenxin_api_key or "",
        "secret_key": settings.wenxin_secret_key or "",
        "model": "ernie-bot-turbo",
        "max_tokens": 1000,
        "temperature": 0.7,
        "enabled": False
    },
    "qianwen": {
        "api_key": settings.qianwen_api_key or "",
        "model": "qwen-turbo",
        "max_tokens": 1000,
        "temperature": 0.7,
        "enabled": False
    },
    "gpt4": {
        "api_key": settings.openai_api_key or "",
        "model": "gpt-3.5-turbo",
        "base_url": settings.openai_base_url,
        "max_tokens": 1000,
        "temperature": 0.7,
        "enabled": False
    }
}

# 路由默认配置
DEFAULT_ROUTING_CONFIG = {
    "default_model": "deepseek",
    "fallback_model": "wenxin",
    "enable_smart_routing": True
}

# 成本控制默认配置
DEFAULT_COST_CONTROL_CONFIG = {
    "daily_limit": settings.daily_cost_limit,
    "warning_threshold": settings.cost_warning_threshold,
    "enable_cost_control": True
}

# 小米默认配置
DEFAULT_XIAOMI_CONFIG = {
    "userId": settings.xiaomi_user_id or "",
    "password": settings.xiaomi_password or "",
    "did": settings.xiaomi_device_did or "",
    "tts": "xiaoai",
    "timeout": 5,
    "exitKeepAliveAfter": 30,
    "checkInterval": 1000,
    "callAIKeywords": ["请", "你", "傻妞"],
    "wakeUpKeywords": ["打开", "进入", "召唤"],
    "exitKeywords": ["关闭", "退出", "再见"],
    "onEnterAI": ["你好，我是傻妞，现在拥有更强大的AI能力了！"],
    "onExitAI": ["傻妞已退出，期待下次对话"],
    "onAIAsking": ["让我想想", "正在思考中"],
    "onAIReplied": ["我说完了", "还有其他问题吗"],
    "onAIError": ["啊哦，出错了，让我换个方式试试"]
}

def get_config_path() -> str:
    """获取配置文件路径"""
    return os.path.join(os.getcwd(), "config")

def ensure_config_dir():
    """确保配置目录存在"""
    config_dir = get_config_path()
    os.makedirs(config_dir, exist_ok=True)
    return config_dir

def get_log_path() -> str:
    """获取日志文件路径"""
    log_dir = os.path.join(os.getcwd(), "logs")
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, "xiaoai.log")
