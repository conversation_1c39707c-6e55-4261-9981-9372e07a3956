# 小爱同学AI管理器后端依赖

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# WebSocket支持
python-socketio==5.10.0
python-socketio[client]==5.10.0

# HTTP客户端
httpx==0.25.0
aiohttp==3.9.0
requests==2.31.0

# 数据处理
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.0
aiosqlite==0.19.0

# 缓存
redis==5.0.1
aioredis==2.0.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志
loguru==0.7.2
structlog==23.2.0

# 安全
cryptography==41.0.7
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# 工具库
asyncio==3.4.3
asyncio-mqtt==0.16.1
websockets==12.0

# Docker相关
docker==6.1.3

# 文件处理
aiofiles==23.2.0

# 时间处理
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10

# 监控
prometheus-client==0.19.0

# 开发工具
pytest==7.4.0
pytest-asyncio==0.21.0
black==23.11.0
flake8==6.1.0
