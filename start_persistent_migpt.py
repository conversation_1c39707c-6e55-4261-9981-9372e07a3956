#!/usr/bin/env python3
"""
持久化MiGPT启动脚本
使用保存的登录状态，避免重复验证
"""

import subprocess
import os
import time
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_mi_json():
    """检查.mi.json文件"""
    mi_json_path = "mi-gpt/.mi.json"
    if os.path.exists(mi_json_path):
        log("✅ 发现登录状态文件，将使用持久化登录")
        return True
    else:
        log("⚠️ 没有登录状态文件，首次启动需要验证")
        return False

def stop_existing_container():
    """停止现有容器"""
    log("🛑 停止现有容器...")
    try:
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True, timeout=10)
        log("✅ 现有容器已停止并删除")
    except Exception as e:
        log(f"⚠️ 停止容器过程中的警告: {e}")

def start_persistent_container():
    """启动持久化容器"""
    log("🚀 启动持久化MiGPT容器...")
    
    # 基础命令
    cmd = [
        "docker", "run", "-d",
        "--name", "xiaoai-llm",
        "--env-file", ".env",
        "-v", f"{os.getcwd()}/mi-gpt/.migpt.js:/app/.migpt.js"
    ]
    
    # 如果有登录状态文件，挂载它
    if check_mi_json():
        cmd.extend(["-v", f"{os.getcwd()}/mi-gpt/.mi.json:/app/.mi.json"])
    
    # 添加镜像名
    cmd.append("idootop/mi-gpt:latest")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            log("✅ 持久化容器启动成功")
            return True
        else:
            log(f"❌ 容器启动失败: {result.stderr}")
            return False
    except Exception as e:
        log(f"❌ 启动容器失败: {e}")
        return False

def monitor_container_startup():
    """监控容器启动状态"""
    log("👀 监控容器启动状态...")
    
    for i in range(6):  # 监控30秒
        time.sleep(5)
        
        try:
            # 检查容器状态
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=xiaoai-llm", "--format", "{{.Status}}"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                status = result.stdout.strip()
                log(f"📊 容器状态: {status}")
                
                if "Up" in status:
                    # 检查日志
                    log_result = subprocess.run(
                        ["docker", "logs", "--tail", "10", "xiaoai-llm"],
                        capture_output=True, text=True, timeout=10
                    )
                    
                    if log_result.returncode == 0:
                        logs = log_result.stdout
                        
                        if "Mi Services 初始化成功" in logs:
                            log("🎉 MiGPT启动成功！无需验证")
                            return "success"
                        elif "触发小米账号异地登录安全验证机制" in logs:
                            log("🛡️ 需要验证（登录状态可能已过期）")
                            return "need_verification"
                        elif "小米账号登录失败" in logs:
                            log("❌ 登录失败")
                            return "login_failed"
                        else:
                            log("⚠️ 启动中...")
                            continue
                else:
                    log("⚠️ 容器未正常运行")
                    continue
            else:
                log("❌ 无法获取容器状态")
                continue
                
        except Exception as e:
            log(f"❌ 监控过程出错: {e}")
            continue
    
    log("⚠️ 监控超时，状态不明确")
    return "unknown"

def handle_verification_needed():
    """处理需要验证的情况"""
    log("🛡️ 检测到需要重新验证...")
    
    print("\n" + "="*50)
    print("📱 登录状态已过期，需要重新验证:")
    print("1. 使用智能验证助手: python smart_verification_helper.py")
    print("2. 或手动获取验证链接: python get_verification_url.py")
    print("3. 验证完成后，新的登录状态会自动保存")
    print("="*50)
    
    choice = input("\n是否立即启动验证助手？(y/n): ").lower().strip()
    if choice == 'y':
        try:
            subprocess.run(["python", "smart_verification_helper.py"], cwd=".")
        except Exception as e:
            log(f"启动验证助手失败: {e}")

def main():
    print("🚀 持久化MiGPT启动器")
    print("="*50)
    
    log("🎯 目标: 使用保存的登录状态，避免重复验证")
    
    # 1. 停止现有容器
    stop_existing_container()
    
    # 2. 启动持久化容器
    if not start_persistent_container():
        log("❌ 容器启动失败")
        return False
    
    # 3. 监控启动状态
    status = monitor_container_startup()
    
    # 4. 处理不同状态
    print("\n" + "="*50)
    if status == "success":
        log("🎉 持久化启动成功！系统正常运行")
        print("✅ MiGPT容器: 正常运行")
        print("✅ 小米账号: 已连接（使用保存的登录状态）")
        print("✅ 无需重复验证")
        
        # 显示访问信息
        print("\n🌐 访问地址:")
        print("- 前端界面: file:///d:/XiaoaiTX/simple-frontend.html")
        print("- 后端API: http://localhost:8000")
        
        return True
        
    elif status == "need_verification":
        log("⚠️ 登录状态已过期，需要重新验证")
        handle_verification_needed()
        return False
        
    elif status == "login_failed":
        log("❌ 登录失败，可能需要检查账号配置")
        return False
        
    else:
        log("⚠️ 启动状态不明确，请检查容器日志")
        print("💡 检查命令: docker logs xiaoai-llm")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("🎉 持久化启动完成！")
    else:
        print("⚠️ 启动过程中遇到问题")
    print("="*50)
