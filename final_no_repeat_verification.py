#!/usr/bin/env python3
"""
最终避免反复验证解决方案
基于MiGPT官方文档的终极解决方案
"""

import subprocess
import requests
import webbrowser
import time
import os
import shutil
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    print("🛡️ 最终避免反复验证解决方案")
    print("="*60)
    
    log("📋 基于MiGPT官方文档的分析:")
    print("1. ✅ '等待1小时'确实是官方说法")
    print("2. ❌ 但反复验证确实不合理")
    print("3. 🎯 根本问题: 小米安全机制过于严格")
    
    print("\n" + "="*60)
    print("🎯 MiGPT官方文档提供的终极解决方案:")
    print("📖 来源: mi-gpt/docs/faq.md 第128行")
    print()
    print("💡 官方建议:")
    print("1. 在本地网络环境下运行MiGPT")
    print("2. 登录成功后导出.mi.json文件")
    print("3. 将.mi.json挂载到容器中")
    print("4. 这样可以避免重复验证")
    print("="*60)
    
    # 检查当前状态
    log("🔍 检查当前登录状态文件...")
    mi_json_exists = os.path.exists("mi-gpt/.mi.json")
    log(f"📋 .mi.json文件: {'存在' if mi_json_exists else '不存在'}")
    
    if mi_json_exists:
        # 备份现有文件
        backup_path = f"mi-gpt/.mi.json.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2("mi-gpt/.mi.json", backup_path)
        log(f"✅ 已备份现有登录状态: {backup_path}")
    
    print("\n" + "="*60)
    print("🚀 执行官方推荐的解决方案:")
    print("="*60)
    
    # 步骤1: 完成一次验证
    log("📱 步骤1: 完成一次验证获取有效登录状态")
    
    # 获取验证链接
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=10)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            if urls:
                latest_url = urls[0]
                log("🔗 获取到验证链接")
                log("🌐 在浏览器中打开验证链接...")
                webbrowser.open(latest_url)
                
                print("\n📱 请在浏览器中完成验证:")
                print("1. 输入手机号码")
                print("2. 输入验证码")
                print("3. 完成安全验证")
                
                input("\n✅ 验证完成后，按回车键继续...")
                
                # 步骤2: 等待验证生效
                log("⏰ 步骤2: 等待验证生效（根据官方说明需要约1小时）")
                
                print("\n💡 根据MiGPT官方文档:")
                print("- 验证成功后需要等待约1小时")
                print("- 这是小米的安全机制，无法绕过")
                print("- 但这是最后一次需要等待！")
                
                choice = input("\n选择操作:\n1. 立即测试（可能失败）\n2. 等待1小时后再测试\n3. 设置定时提醒\n请选择 (1/2/3): ").strip()
                
                if choice == "1":
                    log("🧪 立即测试验证状态...")
                    test_verification_now()
                elif choice == "2":
                    log("⏰ 建议1小时后运行以下命令:")
                    print("python final_no_repeat_verification.py")
                elif choice == "3":
                    create_reminder_script()
                
            else:
                log("❌ 没有可用的验证链接")
        else:
            log("❌ 无法获取验证链接")
    except Exception as e:
        log(f"❌ 获取验证链接失败: {e}")
    
    # 步骤3: 提供长期解决方案
    print("\n" + "="*60)
    print("🎯 长期解决方案（基于官方文档）:")
    print("="*60)
    
    print("1. 📋 使用本地网络环境运行MiGPT")
    print("2. 🔄 验证成功后自动导出.mi.json")
    print("3. 🚀 以后启动时自动挂载登录状态")
    print("4. ✅ 避免重复验证")
    
    # 创建自动化脚本
    create_automated_solution()
    
    print("\n" + "="*60)
    print("🎉 解决方案部署完成！")
    print("💡 关键点:")
    print("- 这是基于MiGPT官方文档的正确解决方案")
    print("- 只需要验证一次，以后不再需要")
    print("- 登录状态会被持久化保存")
    print("="*60)

def test_verification_now():
    """立即测试验证状态"""
    log("🧪 立即测试验证状态...")
    
    try:
        # 启动容器测试
        subprocess.run(["docker", "start", "xiaoai-llm"], capture_output=True, timeout=15)
        time.sleep(15)
        
        # 检查日志
        result = subprocess.run(
            ["docker", "logs", "--tail", "10", "xiaoai-llm"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            logs = result.stdout
            if "Mi Services 初始化成功" in logs:
                log("🎉 验证成功！立即生效")
                
                # 导出登录状态
                subprocess.run(["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"], 
                             capture_output=True, timeout=10)
                log("✅ 登录状态已导出并保存")
                
                return True
            else:
                log("⚠️ 验证尚未生效，需要等待")
                subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
                return False
        else:
            log("❌ 无法获取容器日志")
            return False
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        return False

def create_reminder_script():
    """创建定时提醒脚本"""
    log("⏰ 创建定时提醒脚本...")
    
    script_content = f'''#!/usr/bin/env python3
"""
验证状态检查提醒脚本
在验证完成1小时后自动检查
"""

import time
import subprocess
from datetime import datetime, timedelta

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{{timestamp}}] {{message}}")

def main():
    print("⏰ 验证状态检查提醒")
    print("="*50)
    
    # 等待1小时
    target_time = datetime.now() + timedelta(hours=1)
    log(f"🕐 将在 {{target_time.strftime('%H:%M:%S')}} 自动检查验证状态")
    
    # 每10分钟提醒一次
    while datetime.now() < target_time:
        remaining = target_time - datetime.now()
        minutes = int(remaining.total_seconds() / 60)
        log(f"⏳ 还需等待 {{minutes}} 分钟...")
        time.sleep(600)  # 等待10分钟
    
    log("🔔 时间到！开始检查验证状态...")
    
    # 运行最终验证检查
    subprocess.run(["python", "final_no_repeat_verification.py"], cwd=".")

if __name__ == "__main__":
    main()
'''
    
    with open("verification_reminder.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    log("✅ 已创建提醒脚本: verification_reminder.py")
    log("🚀 运行命令: python verification_reminder.py")

def create_automated_solution():
    """创建自动化解决方案"""
    log("🤖 创建自动化解决方案...")
    
    # 创建智能启动脚本
    script_content = '''#!/usr/bin/env python3
"""
智能MiGPT启动器
自动处理登录状态，避免重复验证
"""

import subprocess
import os
import time
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def smart_start():
    """智能启动MiGPT"""
    log("🤖 智能MiGPT启动器")
    
    # 检查登录状态文件
    mi_json_exists = os.path.exists("mi-gpt/.mi.json")
    
    if mi_json_exists:
        log("✅ 发现登录状态文件，使用持久化登录")
        mount_mi_json = '-v "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json"'
    else:
        log("⚠️ 没有登录状态文件，首次启动需要验证")
        mount_mi_json = ""
    
    # 停止现有容器
    subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
    subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
    
    # 构建启动命令
    cmd = f'''docker run -d --name xiaoai-llm --env-file mi-gpt/.env -v "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js" {mount_mi_json} idootop/mi-gpt:latest'''
    
    # 启动容器
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        log("✅ 容器启动成功")
        
        # 监控启动状态
        time.sleep(15)
        log_result = subprocess.run(["docker", "logs", "--tail", "10", "xiaoai-llm"], 
                                   capture_output=True, text=True)
        
        if "Mi Services 初始化成功" in log_result.stdout:
            log("🎉 MiGPT启动成功！")
            
            # 如果没有登录状态文件，导出一个
            if not mi_json_exists:
                subprocess.run(["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"])
                log("✅ 登录状态已保存，下次启动将更快")
            
        elif "触发小米账号异地登录安全验证机制" in log_result.stdout:
            log("🛡️ 需要验证，请运行: python final_no_repeat_verification.py")
        else:
            log("⚠️ 启动状态不明确，请检查日志")
    else:
        log(f"❌ 容器启动失败: {result.stderr}")

if __name__ == "__main__":
    smart_start()
'''
    
    with open("smart_migpt_starter.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    log("✅ 已创建智能启动器: smart_migpt_starter.py")
    log("🚀 使用方法: python smart_migpt_starter.py")

if __name__ == "__main__":
    main()
