#!/usr/bin/env python3
"""
小米账号验证问题深度诊断工具
分析验证失败的具体原因并提供解决方案
"""

import requests
import subprocess
import json
import time
from datetime import datetime, timedelta

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_verification_urls():
    """检查验证URL的有效性"""
    log("🔗 检查验证URL状态...")
    
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            count = data.get('count', 0)
            
            log(f"📊 检测到 {count} 个验证链接")
            
            if urls:
                latest_url = urls[0]
                log("🔗 最新验证链接:")
                log(f"  {latest_url[:100]}...")
                
                # 测试验证链接的可访问性
                try:
                    test_response = requests.head(latest_url, timeout=10, allow_redirects=True)
                    if test_response.status_code == 200:
                        log("✅ 验证链接可访问")
                    else:
                        log(f"⚠️ 验证链接响应异常: {test_response.status_code}")
                except Exception as e:
                    log(f"❌ 验证链接无法访问: {e}")
                
                return urls
            else:
                log("❌ 没有可用的验证链接")
                return []
        else:
            log(f"❌ 获取验证链接失败: {response.status_code}")
            return []
    except Exception as e:
        log(f"❌ 检查验证URL失败: {e}")
        return []

def analyze_container_logs():
    """分析容器日志中的验证问题"""
    log("📋 分析容器日志...")
    
    try:
        result = subprocess.run(
            ["docker", "logs", "--tail", "50", "xiaoai-llm"],
            capture_output=True, text=True, timeout=15
        )
        
        logs = result.stdout
        
        # 分析不同类型的错误
        issues = []
        
        if "小米账号登录失败" in logs:
            issues.append("🔐 小米账号登录失败")
        
        if "触发小米账号异地登录安全验证机制" in logs:
            issues.append("🛡️ 触发异地登录安全验证")
        
        if "初始化 Mi Services 失败" in logs:
            issues.append("⚙️ Mi Services 初始化失败")
        
        if "Assertion failed" in logs:
            issues.append("💥 断言失败 - 可能是配置问题")
        
        if "授权成功后，大约需要等待 1 个小时左右" in logs:
            issues.append("⏰ 需要等待验证生效（1小时）")
        
        log("🔍 发现的问题:")
        for issue in issues:
            log(f"  {issue}")
        
        # 检查是否有成功的迹象
        if "✅" in logs and "登录成功" in logs:
            log("🎉 发现登录成功的记录")
        elif "Mi Services 初始化成功" in logs:
            log("🎉 发现服务初始化成功的记录")
        
        return issues
        
    except Exception as e:
        log(f"❌ 分析容器日志失败: {e}")
        return []

def check_account_config():
    """检查账号配置"""
    log("🔧 检查小米账号配置...")
    
    try:
        # 检查配置文件
        with open("mi-gpt\\.migpt.js", "r", encoding="utf-8") as f:
            config_content = f.read()
        
        # 检查关键配置项
        config_issues = []
        
        if 'userId: "*********"' in config_content:
            log("✅ 用户ID配置正确")
        else:
            config_issues.append("❌ 用户ID配置异常")
        
        if 'did: "LJXA"' in config_content:
            log("✅ 设备ID配置正确")
        else:
            config_issues.append("❌ 设备ID配置异常")
        
        if 'password:' in config_content:
            log("✅ 密码已配置")
        else:
            config_issues.append("❌ 密码未配置")
        
        return config_issues
        
    except Exception as e:
        log(f"❌ 检查配置失败: {e}")
        return ["❌ 无法读取配置文件"]

def test_xiaomi_api_connectivity():
    """测试小米API连接性"""
    log("🌐 测试小米API连接性...")
    
    xiaomi_apis = [
        "https://account.xiaomi.com",
        "https://api.mina.mi.com",
        "https://userprofile.mina.mi.com"
    ]
    
    connectivity_issues = []
    
    for api in xiaomi_apis:
        try:
            response = requests.get(api, timeout=10)
            if response.status_code == 200:
                log(f"✅ {api} - 连接正常")
            else:
                log(f"⚠️ {api} - 状态码: {response.status_code}")
                connectivity_issues.append(f"API响应异常: {api}")
        except Exception as e:
            log(f"❌ {api} - 连接失败: {e}")
            connectivity_issues.append(f"API连接失败: {api}")
    
    return connectivity_issues

def generate_solutions(issues, config_issues, connectivity_issues):
    """生成解决方案"""
    log("💡 生成解决方案...")
    
    solutions = []
    
    # 基于发现的问题生成对应解决方案
    if "🛡️ 触发异地登录安全验证" in issues:
        solutions.append({
            "问题": "异地登录安全验证",
            "解决方案": [
                "1. 使用智能验证助手: python smart_verification_helper.py",
                "2. 手动访问最新验证链接完成手机号验证",
                "3. 验证完成后等待1小时让小米系统更新状态",
                "4. 如果多次验证失败，尝试更换网络环境"
            ]
        })
    
    if "⏰ 需要等待验证生效（1小时）" in issues:
        solutions.append({
            "问题": "验证等待时间",
            "解决方案": [
                "1. 这是小米的安全机制，必须等待约1小时",
                "2. 可以暂时停止容器避免无限重启: python migpt_manager.py stop",
                "3. 1小时后重新启动: python migpt_manager.py start",
                "4. 使用自动管理模式: python migpt_manager.py auto"
            ]
        })
    
    if config_issues:
        solutions.append({
            "问题": "配置文件问题",
            "解决方案": [
                "1. 检查mi-gpt\\.migpt.js文件中的配置",
                "2. 确保userId、password、did配置正确",
                "3. 重新配置后重启容器",
                "4. 使用配置修复工具: python fix_device_config.py"
            ]
        })
    
    if connectivity_issues:
        solutions.append({
            "问题": "网络连接问题",
            "解决方案": [
                "1. 检查网络连接是否正常",
                "2. 尝试更换DNS服务器",
                "3. 检查防火墙设置",
                "4. 考虑使用VPN或代理"
            ]
        })
    
    # 通用解决方案
    solutions.append({
        "问题": "通用故障排除",
        "解决方案": [
            "1. 重启Docker服务",
            "2. 重新创建MiGPT容器",
            "3. 清除浏览器缓存后重新验证",
            "4. 尝试使用不同的小米账号",
            "5. 检查小米账号是否被锁定或限制"
        ]
    })
    
    return solutions

def main():
    print("🔍 小米账号验证问题深度诊断")
    print("="*60)
    
    # 1. 检查验证URL
    urls = check_verification_urls()
    print()
    
    # 2. 分析容器日志
    issues = analyze_container_logs()
    print()
    
    # 3. 检查账号配置
    config_issues = check_account_config()
    print()
    
    # 4. 测试API连接性
    connectivity_issues = test_xiaomi_api_connectivity()
    print()
    
    # 5. 生成解决方案
    print("="*60)
    solutions = generate_solutions(issues, config_issues, connectivity_issues)
    
    log("🎯 推荐解决方案:")
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['问题']}:")
        for step in solution['解决方案']:
            print(f"   {step}")
    
    # 6. 提供快速操作建议
    print("\n" + "="*60)
    log("⚡ 快速操作建议:")
    
    if "🛡️ 触发异地登录安全验证" in issues:
        print("  🚀 立即执行: python smart_verification_helper.py")
        print("  ⏰ 然后等待: 验证完成后等待1小时")
        print("  🔄 最后重启: python migpt_manager.py start")
    elif "⏰ 需要等待验证生效（1小时）" in issues:
        print("  ⏰ 当前状态: 验证已完成，等待生效中")
        print("  🛑 建议操作: python migpt_manager.py stop (避免重启)")
        print("  ⏰ 等待时间: 约1小时后重新启动")
    else:
        print("  🔧 检查配置: 确认账号密码正确")
        print("  🌐 检查网络: 确保能访问小米服务")
        print("  🔄 重新验证: python smart_verification_helper.py")
    
    print("\n" + "="*60)
    log("🎉 诊断完成！请根据上述建议进行操作")

if __name__ == "__main__":
    main()
