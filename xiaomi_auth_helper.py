#!/usr/bin/env python3
"""
小米账号安全验证助手
自动监控MiGPT日志，检测到验证需求时自动打开浏览器
"""

import re
import time
import webbrowser
import subprocess
import threading
import json
from datetime import datetime
from urllib.parse import unquote
import tkinter as tk
from tkinter import messagebox, scrolledtext
import sys
import os

class XiaomiAuthHelper:
    def __init__(self):
        self.container_name = "xiaoai-llm"
        self.verification_urls = []
        self.is_monitoring = False
        self.log_thread = None
        
        # 创建GUI
        self.setup_gui()
        
    def setup_gui(self):
        """设置图形界面"""
        self.root = tk.Tk()
        self.root.title("小米账号安全验证助手")
        self.root.geometry("800x600")
        
        # 标题
        title_label = tk.Label(self.root, text="小米账号安全验证助手", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="状态: 未启动", 
                                    font=("Arial", 12))
        self.status_label.pack(pady=5)
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        # 启动监控按钮
        self.start_btn = tk.Button(button_frame, text="启动监控", 
                                  command=self.start_monitoring,
                                  bg="green", fg="white", font=("Arial", 12))
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止监控按钮
        self.stop_btn = tk.Button(button_frame, text="停止监控", 
                                 command=self.stop_monitoring,
                                 bg="red", fg="white", font=("Arial", 12))
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # 重启容器按钮
        self.restart_btn = tk.Button(button_frame, text="重启MiGPT", 
                                    command=self.restart_container,
                                    bg="orange", fg="white", font=("Arial", 12))
        self.restart_btn.pack(side=tk.LEFT, padx=5)
        
        # 手动验证按钮
        self.manual_btn = tk.Button(button_frame, text="手动打开验证", 
                                   command=self.manual_verification,
                                   bg="blue", fg="white", font=("Arial", 12))
        self.manual_btn.pack(side=tk.LEFT, padx=5)
        
        # 日志显示区域
        log_label = tk.Label(self.root, text="实时日志:", font=("Arial", 12, "bold"))
        log_label.pack(anchor="w", padx=10, pady=(20, 5))
        
        self.log_text = scrolledtext.ScrolledText(self.root, height=20, width=100)
        self.log_text.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)
        
        # 验证URL显示
        url_label = tk.Label(self.root, text="检测到的验证链接:", font=("Arial", 12, "bold"))
        url_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.url_text = scrolledtext.ScrolledText(self.root, height=5, width=100)
        self.url_text.pack(padx=10, pady=5, fill=tk.X)
        
    def log_message(self, message):
        """在GUI中显示日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()
        
        # 同时打印到控制台
        print(log_entry.strip())
    
    def update_status(self, status):
        """更新状态显示"""
        self.status_label.config(text=f"状态: {status}")
        self.root.update()
    
    def extract_verification_urls(self, log_line):
        """从日志中提取验证URL"""
        # 匹配验证URL的正则表达式
        patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)',
            r'"notificationUrl":"(https://account\.xiaomi\.com/[^"]+)"'
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, log_line)
            for match in matches:
                # URL解码
                decoded_url = unquote(match)
                if decoded_url not in urls:
                    urls.append(decoded_url)
        
        return urls
    
    def open_verification_url(self, url):
        """在浏览器中打开验证URL"""
        try:
            self.log_message(f"🌐 正在打开验证页面...")
            webbrowser.open(url)
            
            # 在URL显示区域添加链接
            self.url_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {url}\n")
            self.url_text.see(tk.END)
            
            # 显示提示对话框
            messagebox.showinfo("验证提醒", 
                              "验证页面已在浏览器中打开！\n\n"
                              "请按照页面提示完成手机号验证。\n"
                              "验证成功后需要等待约1小时才能生效。")
            
            return True
        except Exception as e:
            self.log_message(f"❌ 打开浏览器失败: {e}")
            return False
    
    def monitor_docker_logs(self):
        """监控Docker容器日志"""
        try:
            # 启动docker logs命令
            cmd = ["docker", "logs", "-f", self.container_name]
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.log_message(f"📊 开始监控容器 {self.container_name} 的日志...")
            
            while self.is_monitoring:
                try:
                    line = process.stdout.readline()
                    if not line:
                        break
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 显示重要日志
                    if any(keyword in line for keyword in ["🔥", "👉", "❌", "✅", "MiGPT"]):
                        self.log_message(line)
                    
                    # 检测验证需求
                    if "触发小米账号异地登录安全验证机制" in line:
                        self.log_message("🚨 检测到需要安全验证！")
                        self.update_status("需要验证")
                    
                    # 提取验证URL
                    urls = self.extract_verification_urls(line)
                    for url in urls:
                        if url not in self.verification_urls:
                            self.verification_urls.append(url)
                            self.log_message(f"🔗 发现验证链接，正在自动打开...")
                            self.open_verification_url(url)
                    
                    # 检测登录成功
                    if "✅" in line and ("登录成功" in line or "连接成功" in line):
                        self.log_message("🎉 登录成功！")
                        self.update_status("运行正常")
                    
                    # 检测登录失败
                    if "❌ 小米账号登录失败" in line:
                        self.log_message("⚠️ 登录失败，可能需要重新验证")
                        self.update_status("登录失败")
                
                except Exception as e:
                    if self.is_monitoring:
                        self.log_message(f"❌ 读取日志时出错: {e}")
                    break
            
            process.terminate()
            
        except subprocess.CalledProcessError as e:
            self.log_message(f"❌ 无法启动日志监控: {e}")
        except Exception as e:
            self.log_message(f"❌ 监控过程中出错: {e}")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            self.log_message("⚠️ 监控已在运行中")
            return
        
        # 检查Docker容器是否存在
        try:
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            
            if self.container_name not in result.stdout:
                self.log_message(f"❌ 容器 {self.container_name} 不存在，请先启动MiGPT")
                messagebox.showerror("错误", f"容器 {self.container_name} 不存在\n请先启动MiGPT服务")
                return
        except subprocess.CalledProcessError:
            self.log_message("❌ 无法检查Docker容器状态")
            return
        
        self.is_monitoring = True
        self.update_status("监控中")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 在新线程中启动监控
        self.log_thread = threading.Thread(target=self.monitor_docker_logs, daemon=True)
        self.log_thread.start()
        
        self.log_message("🚀 验证助手已启动，正在监控MiGPT日志...")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.update_status("已停止")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.log_message("⏹️ 监控已停止")
    
    def restart_container(self):
        """重启MiGPT容器"""
        try:
            self.log_message("🔄 正在重启MiGPT容器...")
            
            # 停止容器
            subprocess.run(["docker", "stop", self.container_name], 
                          capture_output=True, check=True)
            
            # 启动容器
            subprocess.run(["docker", "start", self.container_name], 
                          capture_output=True, check=True)
            
            self.log_message("✅ MiGPT容器重启成功")
            
            # 清空之前的验证URL
            self.verification_urls.clear()
            
        except subprocess.CalledProcessError as e:
            self.log_message(f"❌ 重启容器失败: {e}")
            messagebox.showerror("错误", f"重启容器失败: {e}")
    
    def manual_verification(self):
        """手动打开最近的验证链接"""
        if not self.verification_urls:
            messagebox.showwarning("提醒", "暂无检测到验证链接\n请先启动监控或重启MiGPT")
            return
        
        latest_url = self.verification_urls[-1]
        self.log_message("🔗 手动打开最新的验证链接...")
        self.open_verification_url(latest_url)
    
    def run(self):
        """运行GUI"""
        self.log_message("🎯 小米账号安全验证助手已启动")
        self.log_message("💡 使用说明:")
        self.log_message("   1. 点击'启动监控'开始监控MiGPT日志")
        self.log_message("   2. 检测到验证需求时会自动打开浏览器")
        self.log_message("   3. 在浏览器中完成手机号验证")
        self.log_message("   4. 验证成功后等待约1小时生效")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("👋 程序已退出")
        finally:
            self.is_monitoring = False

def main():
    """主函数"""
    print("🚀 启动小米账号安全验证助手...")
    
    # 检查是否安装了Docker
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未检测到Docker，请先安装Docker")
        input("按回车键退出...")
        return
    
    # 启动验证助手
    helper = XiaomiAuthHelper()
    helper.run()

if __name__ == "__main__":
    main()
