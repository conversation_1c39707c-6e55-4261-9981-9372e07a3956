#!/usr/bin/env python3
"""
简单启动脚本 - 一键启动所有服务并打开验证链接
"""

import subprocess
import time
import webbrowser
import requests
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def start_backend():
    """启动后端服务"""
    log("🚀 启动后端服务...")
    try:
        # 启动后端
        process = subprocess.Popen(
            ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"],
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待启动
        time.sleep(5)
        
        # 测试连接
        response = requests.get("http://localhost:8000", timeout=5)
        if response.status_code == 200:
            log("✅ 后端服务启动成功")
            return True
        else:
            log("❌ 后端服务启动失败")
            return False
    except Exception as e:
        log(f"❌ 后端启动异常: {e}")
        return False

def get_verification_url():
    """获取验证链接"""
    log("🔗 获取验证链接...")
    try:
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=10)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            if urls:
                log(f"✅ 获取到 {len(urls)} 个验证链接")
                return urls[0]
            else:
                log("⚠️ 没有可用的验证链接")
                return None
        else:
            log(f"❌ 获取验证链接失败: {response.status_code}")
            return None
    except Exception as e:
        log(f"❌ 获取验证链接异常: {e}")
        return None

def open_verification_url(url):
    """打开验证链接"""
    log("🌐 在浏览器中打开验证链接...")
    try:
        webbrowser.open(url)
        log("✅ 验证链接已在浏览器中打开")
        return True
    except Exception as e:
        log(f"❌ 打开验证链接失败: {e}")
        return False

def start_migpt_container():
    """启动MiGPT容器"""
    log("📦 启动MiGPT容器...")
    try:
        # 停止现有容器
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
        
        # 启动新容器
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js"
        ]
        
        # 如果有登录状态文件，挂载它
        import os
        if os.path.exists("mi-gpt/.mi.json"):
            cmd.extend(["-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json"])
            log("✅ 使用持久化登录状态")
        
        cmd.append("idootop/mi-gpt:latest")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            log("✅ MiGPT容器启动成功")
            return True
        else:
            log(f"❌ MiGPT容器启动失败: {result.stderr}")
            return False
    except Exception as e:
        log(f"❌ 容器启动异常: {e}")
        return False

def main():
    print("🚀 小爱同学AI管理系统 - 一键启动")
    print("="*60)
    
    # 1. 启动后端服务
    if not start_backend():
        print("❌ 后端启动失败，请检查配置")
        return
    
    # 2. 获取验证链接
    verification_url = get_verification_url()
    if not verification_url:
        print("❌ 无法获取验证链接，请检查后端服务")
        return
    
    # 3. 打开验证链接
    print("\n" + "="*60)
    print("🛡️ 小米账号验证")
    print("="*60)
    print("📱 即将在浏览器中打开小米账号验证页面")
    print("请按照页面提示完成验证:")
    print("1. 输入手机号码")
    print("2. 输入短信验证码")
    print("3. 完成安全验证")
    print("="*60)
    
    input("按回车键在浏览器中打开验证页面...")
    
    if open_verification_url(verification_url):
        print("\n✅ 验证页面已打开，请在浏览器中完成验证")
    else:
        print(f"\n❌ 自动打开失败，请手动复制以下链接到浏览器:")
        print(verification_url)
    
    # 4. 等待用户完成验证
    print("\n" + "="*60)
    print("⏰ 等待验证完成")
    print("="*60)
    input("验证完成后，按回车键继续...")
    
    # 5. 启动MiGPT容器
    if start_migpt_container():
        print("\n✅ MiGPT容器启动成功")
        
        # 6. 等待初始化
        log("⏰ 等待容器初始化...")
        time.sleep(15)
        
        # 7. 检查容器状态
        try:
            result = subprocess.run(
                ["docker", "logs", "--tail", "10", "xiaoai-llm"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                logs = result.stdout
                print("\n📋 容器日志:")
                print(logs)
                
                if "Mi Services 初始化成功" in logs:
                    print("\n🎉 系统启动完全成功！")
                    print("✅ 小米账号验证通过")
                    print("✅ MiGPT服务正常运行")
                elif "触发小米账号异地登录安全验证机制" in logs:
                    print("\n⚠️ 验证尚未生效，需要等待")
                    print("💡 根据小米官方说明，验证成功后需要等待约1小时")
                    print("💡 请稍后重新运行此脚本")
                else:
                    print("\n⚠️ 容器状态不明确，请检查日志")
            else:
                print("\n❌ 无法获取容器日志")
        except Exception as e:
            print(f"\n❌ 检查容器状态失败: {e}")
    else:
        print("\n❌ MiGPT容器启动失败")
    
    # 8. 打开前端界面
    print("\n" + "="*60)
    print("🌐 打开管理界面")
    print("="*60)
    
    frontend_path = "file:///d:/XiaoaiTX/simple-frontend.html"
    try:
        webbrowser.open(frontend_path)
        print("✅ 管理界面已在浏览器中打开")
    except Exception as e:
        print(f"❌ 自动打开失败: {e}")
        print(f"请手动打开: {frontend_path}")
    
    print("\n" + "="*60)
    print("🎉 启动流程完成！")
    print("📊 系统状态:")
    print("- 后端API: http://localhost:8000")
    print("- 前端界面: 已在浏览器中打开")
    print("- MiGPT容器: 已启动")
    print("="*60)

if __name__ == "__main__":
    main()
