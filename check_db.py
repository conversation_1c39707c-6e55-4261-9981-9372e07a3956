#!/usr/bin/env python3
"""
检查数据库配置
"""

import sqlite3
import json
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_database():
    """检查数据库配置"""
    try:
        # 尝试两个可能的数据库文件
        db_files = ["xiaoai.db", "backend/xiaoai.db", "backend/app.db"]

        for db_file in db_files:
            try:
                log(f"🔍 检查数据库: {db_file}")
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                log(f"  表: {[t[0] for t in tables]}")

                # 如果有configurations表，检查配置
                if any('configurations' in str(t) for t in tables):
                    cursor.execute("SELECT key, value FROM configurations")
                    configs = cursor.fetchall()

                    log(f"  📋 配置项 ({len(configs)} 个):")
                    for key, value in configs:
                        try:
                            parsed_value = json.loads(value)
                            log(f"    {key}: {json.dumps(parsed_value, indent=2, ensure_ascii=False)}")
                        except:
                            log(f"    {key}: {value}")

                conn.close()
                break  # 找到有效数据库就停止

            except Exception as e:
                log(f"  ❌ 无法访问: {e}")
                continue
        
    except Exception as e:
        log(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    print("🔍 数据库配置检查")
    print("="*50)
    check_database()
