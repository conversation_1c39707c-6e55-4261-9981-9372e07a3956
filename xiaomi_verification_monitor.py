#!/usr/bin/env python3
"""
小米账号验证监控器
实时监控MiGPT日志，自动打开验证页面
"""

import re
import time
import webbrowser
import subprocess
import threading
from datetime import datetime
from urllib.parse import unquote

class XiaomiVerificationMonitor:
    def __init__(self):
        self.container_name = "xiaoai-llm"
        self.verification_urls = set()  # 使用set避免重复
        self.opened_urls = set()  # 记录已打开的URL
        self.is_monitoring = False
        self.last_verification_time = 0  # 防止频繁验证
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_verification_urls(self, log_line):
        """从日志中提取验证URL"""
        patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)',
            r'"notificationUrl":"(https://account\.xiaomi\.com/[^"]+)"'
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, log_line)
            for match in matches:
                decoded_url = unquote(match)
                if decoded_url not in urls:
                    urls.append(decoded_url)
        
        return urls
    
    def open_verification_url(self, url):
        """在浏览器中打开验证URL"""
        # 检查是否已经打开过这个URL
        if url in self.opened_urls:
            self.log("⚠️ 此验证链接已经打开过，跳过重复打开")
            return False

        # 检查时间间隔，避免频繁打开
        current_time = time.time()
        if current_time - self.last_verification_time < 30:  # 30秒内不重复打开
            self.log("⚠️ 验证间隔太短，跳过此次打开")
            return False

        try:
            self.log("🌐 检测到新的验证需求，正在打开浏览器...")
            webbrowser.open(url)
            self.opened_urls.add(url)  # 记录已打开的URL
            self.last_verification_time = current_time

            self.log("✅ 验证页面已在浏览器中打开")
            self.log("📱 请在浏览器中完成手机号验证")
            self.log("⏰ 验证成功后需要等待约1小时才能生效")
            print("\n" + "="*60)
            print("🔗 验证链接 (已自动打开):")
            print(url[:100] + "..." if len(url) > 100 else url)
            print("="*60 + "\n")
            return True
        except Exception as e:
            self.log(f"❌ 打开浏览器失败: {e}")
            return False
    
    def monitor_docker_logs(self):
        """监控Docker日志"""
        try:
            self.log(f"📊 开始监控容器 {self.container_name} 的日志...")
            self.log("💡 检测到验证需求时会自动打开浏览器")
            self.log("🔍 按 Ctrl+C 停止监控")
            print()
            
            # 启动docker logs命令
            cmd = ["docker", "logs", "-f", self.container_name]
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='ignore'
            )
            
            while self.is_monitoring:
                try:
                    line = process.stdout.readline()
                    if not line:
                        break
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 显示重要日志
                    if any(keyword in line for keyword in ["🔥", "👉", "❌", "✅", "MiGPT", "触发小米账号"]):
                        print(line)
                    
                    # 检测验证需求
                    if "触发小米账号异地登录安全验证机制" in line:
                        self.log("🚨 检测到需要安全验证！")
                    
                    # 提取并打开验证URL
                    urls = self.extract_verification_urls(line)
                    for url in urls:
                        if url not in self.verification_urls:
                            self.verification_urls.add(url)
                            # 只有成功打开才记录
                            if self.open_verification_url(url):
                                self.log(f"📝 已记录验证URL: ...{url[-50:]}")  # 只显示URL末尾
                    
                    # 检测登录状态
                    if "✅" in line and ("登录成功" in line or "连接成功" in line):
                        self.log("🎉 登录成功！")
                    
                    if "❌ 小米账号登录失败" in line:
                        self.log("⚠️ 登录失败，可能需要重新验证")
                
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.log(f"❌ 读取日志时出错: {e}")
                    break
            
            process.terminate()
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 无法启动日志监控: {e}")
        except KeyboardInterrupt:
            self.log("👋 监控已停止")
    
    def start_monitoring(self):
        """启动监控"""
        self.is_monitoring = True
        
        # 在新线程中启动监控
        monitor_thread = threading.Thread(target=self.monitor_docker_logs, daemon=True)
        monitor_thread.start()
        
        try:
            # 主线程等待
            while self.is_monitoring:
                time.sleep(1)
        except KeyboardInterrupt:
            self.log("👋 程序已退出")
            self.is_monitoring = False
    
    def run(self):
        """运行验证监控器"""
        print("🚀 小米账号验证监控器")
        print("="*50)
        
        # 检查Docker
        try:
            subprocess.run(["docker", "--version"], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("❌ 未检测到Docker，请先安装Docker")
            return False
        
        # 检查容器
        try:
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            
            if self.container_name not in result.stdout:
                self.log(f"❌ 容器 {self.container_name} 不存在")
                return False
        except subprocess.CalledProcessError:
            self.log("❌ 无法检查Docker容器状态")
            return False
        
        # 开始监控
        self.start_monitoring()
        
        return True

def main():
    """主函数"""
    monitor = XiaomiVerificationMonitor()
    
    try:
        monitor.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")

if __name__ == "__main__":
    main()
