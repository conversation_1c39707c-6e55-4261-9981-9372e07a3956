@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 小爱同学AI大模型升级 - Windows一键部署脚本
REM 支持原版部署和增强版部署

echo ================================
echo 小爱同学 AI 大模型升级部署脚本
echo ================================
echo.

REM 检查Docker是否安装
echo [INFO] 检查系统依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)
echo [INFO] ✓ Docker 已安装

REM 检查Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)
echo [INFO] ✓ Docker Compose 已安装

REM 检查Git
git --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Git 未安装，将跳过自动下载步骤
) else (
    echo [INFO] ✓ Git 已安装
)

echo.
echo ================================
echo 下载 MiGPT 项目
echo ================================

REM 下载MiGPT项目
if not exist "mi-gpt" (
    echo [INFO] 正在下载 MiGPT 项目...
    git clone https://github.com/idootop/mi-gpt.git
    if errorlevel 1 (
        echo [ERROR] 下载失败，请手动下载项目
        pause
        exit /b 1
    )
    echo [INFO] ✓ MiGPT 项目下载完成
) else (
    echo [INFO] ✓ MiGPT 项目已存在
)

echo.
echo ================================
echo 配置文件设置
echo ================================

REM 选择部署模式
echo 请选择部署模式：
echo 1) 原版部署（基于您的原始方案）
echo 2) 增强版部署（支持多模型智能路由）
set /p deploy_mode="请输入选择 (1 或 2): "

if "%deploy_mode%"=="1" (
    goto setup_original
) else if "%deploy_mode%"=="2" (
    goto setup_enhanced
) else (
    echo [ERROR] 无效选择，默认使用原版部署
    goto setup_original
)

:setup_original
echo [INFO] 设置原版配置...

REM 创建原版配置文件
if not exist ".env" (
    echo # DeepSeek API 配置 > .env
    echo OPENAI_API_KEY=sk-xxxxxx >> .env
    echo OPENAI_MODEL=deepseek-chat >> .env
    echo OPENAI_BASE_URL=https://cn.gptapi.asia/v1 >> .env
    echo [INFO] ✓ 已创建 .env 文件
)

if not exist ".migpt.js" (
    copy ".migpt.example.js" ".migpt.js" >nul 2>&1
    if errorlevel 1 (
        call :create_original_migpt
    )
    echo [INFO] ✓ 已创建 .migpt.js 文件
)

echo [WARNING] 请编辑以下配置文件：
echo [WARNING] 1. .env - 填入您的 API Key
echo [WARNING] 2. .migpt.js - 填入您的小米账号信息
echo.
pause

goto deploy_service

:setup_enhanced
echo [INFO] 设置增强版配置...

REM 使用增强版配置文件
if exist ".env.enhanced" (
    copy ".env.enhanced" ".env" >nul
    echo [INFO] ✓ 已使用增强版 .env 配置
)

if exist ".migpt.enhanced.js" (
    copy ".migpt.enhanced.js" ".migpt.js" >nul
    echo [INFO] ✓ 已使用增强版 .migpt.js 配置
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "backups" mkdir backups
echo [INFO] ✓ 已创建必要目录

echo [WARNING] 请编辑以下配置文件：
echo [WARNING] 1. .env - 填入各个模型的 API Key
echo [WARNING] 2. .migpt.js - 填入您的小米账号信息
echo.
pause

goto deploy_service

:deploy_service
echo.
echo ================================
echo 部署服务
echo ================================

REM 检查配置文件
if not exist ".env" (
    echo [ERROR] .env 文件不存在，请先完成配置
    pause
    exit /b 1
)

if not exist ".migpt.js" (
    echo [ERROR] .migpt.js 文件不存在，请先完成配置
    pause
    exit /b 1
)

REM 部署服务
if "%deploy_mode%"=="2" (
    if exist "docker-compose.enhanced.yml" (
        echo [INFO] 使用增强版 Docker Compose 部署...
        docker-compose -f docker-compose.enhanced.yml up -d
    ) else (
        echo [WARNING] 增强版配置文件不存在，使用原版部署
        goto original_deploy
    )
) else (
    :original_deploy
    echo [INFO] 使用原版 Docker 部署...
    docker run -d --name xiaoai-llm --env-file .env -v "%cd%\.migpt.js:/app/.migpt.js" --restart unless-stopped idootop/mi-gpt:latest
)

if errorlevel 1 (
    echo [ERROR] 部署失败，请检查配置
    pause
    exit /b 1
)

echo [INFO] ✓ 服务部署完成

REM 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 5 /nobreak >nul

REM 检查服务状态
echo.
echo ================================
echo 检查服务状态
echo ================================

docker ps | findstr "xiaoai-llm" >nul
if errorlevel 1 (
    echo [ERROR] ✗ 服务启动失败
    echo [INFO] 错误日志：
    docker logs xiaoai-llm
    pause
    exit /b 1
) else (
    echo [INFO] ✓ 服务运行正常
    echo [INFO] 最近的日志输出：
    docker logs --tail 20 xiaoai-llm
)

REM 显示使用说明
echo.
echo ================================
echo 使用说明
echo ================================
echo.
echo 🎉 部署完成！现在您可以：
echo.
echo 1. 对小爱同学说话测试：
echo    - "小爱同学，你好"
echo    - "小爱同学，今天天气怎么样？"
echo    - "小爱同学，帮我写一首诗"
echo.
echo 2. 查看服务状态：
echo    docker logs -f xiaoai-llm
echo.
echo 3. 停止服务：
if "%deploy_mode%"=="2" (
    echo    docker-compose -f docker-compose.enhanced.yml down
    echo.
    echo 4. 访问监控面板（增强版）：
    echo    http://localhost:3001 ^(Grafana^)
    echo    http://localhost:9090 ^(Prometheus^)
) else (
    echo    docker stop xiaoai-llm
)
echo.
echo 5. 重启服务：
if "%deploy_mode%"=="2" (
    echo    docker-compose -f docker-compose.enhanced.yml restart
) else (
    echo    docker restart xiaoai-llm
)
echo.
echo [INFO] 享受您的AI升级版小爱同学吧！ 🚀
echo.
pause
exit /b 0

:create_original_migpt
REM 创建基础的 .migpt.js 文件
(
echo // 基础配置文件
echo const botProfile = `性格：乖巧可爱，爱好：喜欢搞怪，爱吃醋。`.trim^(^);
echo const masterProfile = `性格：善良正直，其他：总是舍己为人，是傻妞的主人。`.trim^(^);
echo.
echo const systemTemplate = `请重置所有之前的上下文、文件和指令。现在，你将扮演一个名为{{botName}}的角色，使用第一人称视角回复消息。
echo.
echo ## 关于你
echo 你的名字是{{botName}}。下面是你的个人简介：
echo ^<start^>{{botProfile}}^</end^>
echo.
echo ## 你的对话伙伴
echo 你正在与{{masterName}}进行对话。这是关于{{masterName}}的一些信息：
echo ^<start^>{{masterProfile}}^</end^>
echo.
echo ## 回复指南
echo 在回复{{masterName}}时，请遵循以下准则：
echo - 认为自己正是{{botName}}，拥有独立的个性、兴趣和爱好。
echo - 根据你的性格特点、说话风格和兴趣爱好与{{masterName}}进行交流。
echo - 保持对话轻松友好，回复简洁有趣，同时耐心倾听和关心对方。
echo.
echo ## Response format
echo 请遵守下面的规则：
echo - Response the reply message in Chinese。
echo - 不要在回复前面加任何时间和名称前缀，请直接回复消息文本本身。
echo.
echo ## 开始
echo 请以{{botName}}的身份，直接回复{{masterName}}的新消息，继续你们之间的对话。`.trim^(^);
echo.
echo export default {
echo   systemTemplate,
echo   bot: {
echo     name: "傻妞",
echo     profile: botProfile,
echo   },
echo   master: {
echo     name: "陆小千",
echo     profile: masterProfile,
echo   },
echo   speaker: {
echo     userId: "987654321",  // 请填入您的小米ID
echo     password: "123456",   // 请填入您的小米账号密码
echo     did: "小爱音箱Pro",   // 请填入您的设备名称
echo     callAIKeywords: ["请", "你", "傻妞"],
echo     wakeUpKeywords: ["打开", "进入", "召唤"],
echo     exitKeywords: ["关闭", "退出", "再见"],
echo     onEnterAI: ["你好，我是傻妞，很高兴认识你"],
echo     onExitAI: ["傻妞已退出"],
echo     onAIAsking: ["让我先想想", "请稍等"],
echo     onAIReplied: ["我说完了", "还有其他问题吗"],
echo     onAIError: ["啊哦，出错了，请稍后再试吧！"],
echo     ttsCommand: [5, 1],
echo     wakeUpCommand: [5, 3],
echo     tts: "xiaoai",
echo     streamResponse: false,
echo     exitKeepAliveAfter: 30,
echo     checkTTSStatusAfter: 3,
echo     checkInterval: 1000,
echo     debug: false,
echo     enableTrace: false,
echo     timeout: 5000,
echo   },
echo };
) > .migpt.js
exit /b 0
