# OpenAI（也支持通义千问、MoonShot、DeepSeek 等模型）
OPENAI_MODEL=gpt-4o-mini
OPENAI_API_KEY=sk-proj-xxxxxxxxxxxxxxx
# 你的大模型服务接口，比如：https://api.openai.com/v1（注意：一般以 /v1 结尾）
# OPENAI_BASE_URL=https://api.openai.com/v1

# Azure OpenAI Service（可选）
# OPENAI_API_VERSION=2024-04-01-preview
# AZURE_OPENAI_API_KEY=你的密钥
# AZURE_OPENAI_ENDPOINT=https://你的资源名.openai.azure.com
# AZURE_OPENAI_DEPLOYMENT=你的模型部署名，比如：gpt-35-turbo-instruct

# 提示音效（可选，一般不用填，你也可以换上自己的提示音链接试试看效果）
# AUDIO_SILENT=静音音频链接，示例：https://example.com/slient.wav
# AUDIO_BEEP=默认提示音链接，同上
# AUDIO_ACTIVE=唤醒提示音链接，同上
# AUDIO_ERROR=出错了提示音链接，同上

# 第三方 TTS（可选，用于调用第三方 TTS 服务）
# TTS_BASE_URL=http://[你的局域网或公网地址]:[端口号]/[SECRET_PATH]/api
# 比如：http://**************:4321/xxxx/api（注意：不要使用 localhost 或 127.0.0.1）

# 通义千问模型在生成文本时是否使用互联网搜索结果进行参考
# qwen-vl系列、qwen开源系列与qwen-long模型暂时不支持配置该参数
# QWEN_ENABLE_SEARCH=true