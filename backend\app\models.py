"""
数据模型定义
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

# 请求模型

class ChatRequest(BaseModel):
    """聊天请求"""
    message: str = Field(..., description="用户消息")
    model: str = Field(default="deepseek", description="使用的AI模型")

class ModelConfigRequest(BaseModel):
    """模型配置请求"""
    deepseek: Optional[Dict[str, Any]] = None
    wenxin: Optional[Dict[str, Any]] = None
    qianwen: Optional[Dict[str, Any]] = None
    gpt4: Optional[Dict[str, Any]] = None
    routing: Optional[Dict[str, Any]] = None
    cost_control: Optional[Dict[str, Any]] = None

class TestModelRequest(BaseModel):
    """测试模型请求"""
    model: str = Field(..., description="要测试的模型名称")

class XiaomiConfigRequest(BaseModel):
    """小米配置请求"""
    userId: str = Field(..., description="小米用户ID")
    password: str = Field(..., description="小米账号密码")
    did: str = Field(..., description="设备DID或名称")
    tts: Optional[str] = Field(default="xiaoai", description="TTS引擎")
    timeout: Optional[int] = Field(default=5, description="超时时间")
    exitKeepAliveAfter: Optional[int] = Field(default=30, description="退出保持时间")
    checkInterval: Optional[int] = Field(default=1000, description="检查间隔")

# 响应模型

class StatusResponse(BaseModel):
    """状态响应"""
    migpt_status: str
    docker_status: str
    xiaomi_login: str
    ai_model: str

class StatisticsResponse(BaseModel):
    """统计响应"""
    total_conversations: int = 0
    total_api_calls: int = 0
    daily_cost: float = 0.0
    success_rate: float = 0.0
    avg_response_time: float = 0.0

class ChatResponse(BaseModel):
    """聊天响应"""
    response: str
    model: str
    cost: Optional[float] = None
    tokens: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ModelTestResponse(BaseModel):
    """模型测试响应"""
    success: bool
    message: str
    response_time: Optional[float] = None
    cost: Optional[float] = None

class XiaomiDevice(BaseModel):
    """小米设备"""
    did: str
    name: str
    model: str
    type: str
    online: bool
    ip: Optional[str] = None
    mac: Optional[str] = None
    firmware_version: Optional[str] = None

class XiaomiTestResponse(BaseModel):
    """小米连接测试响应"""
    success: bool
    message: str
    account_info: Optional[Dict[str, Any]] = None

# 数据库模型

class ChatMessage(BaseModel):
    """聊天消息"""
    id: Optional[int] = None
    user_id: str
    message: str
    response: str
    model: str
    cost: Optional[float] = None
    tokens: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ModelUsage(BaseModel):
    """模型使用记录"""
    id: Optional[int] = None
    model: str
    api_calls: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    success_count: int = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    date: datetime = Field(default_factory=datetime.now)

class SystemLog(BaseModel):
    """系统日志"""
    id: Optional[int] = None
    level: str
    message: str
    source: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class Configuration(BaseModel):
    """配置"""
    id: Optional[int] = None
    key: str
    value: Dict[str, Any]
    updated_at: datetime = Field(default_factory=datetime.now)

# WebSocket消息模型

class SocketMessage(BaseModel):
    """Socket消息"""
    type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)

class VerificationMessage(BaseModel):
    """验证消息"""
    url: str
    timestamp: datetime = Field(default_factory=datetime.now)

class LogMessage(BaseModel):
    """日志消息"""
    level: str
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)

class StatusMessage(BaseModel):
    """状态消息"""
    service: str
    status: str
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

# 配置模型

class ModelConfig(BaseModel):
    """AI模型配置"""
    api_key: str
    secret_key: Optional[str] = None
    model: str
    base_url: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.7
    enabled: bool = True

class RoutingConfig(BaseModel):
    """路由配置"""
    default_model: str = "deepseek"
    fallback_model: str = "wenxin"
    enable_smart_routing: bool = True

class CostControlConfig(BaseModel):
    """成本控制配置"""
    daily_limit: float = 5.0
    warning_threshold: float = 0.8
    enable_cost_control: bool = True

class XiaomiConfig(BaseModel):
    """小米配置"""
    userId: str
    password: str
    did: str
    tts: str = "xiaoai"
    timeout: int = 5
    exitKeepAliveAfter: int = 30
    checkInterval: int = 1000
    callAIKeywords: List[str] = ["请", "你", "傻妞"]
    wakeUpKeywords: List[str] = ["打开", "进入", "召唤"]
    exitKeywords: List[str] = ["关闭", "退出", "再见"]

# 使用历史模型

class UsageHistory(BaseModel):
    """使用历史"""
    time: str
    conversations: int
    api_calls: int
    cost: float

class ModelUsageDistribution(BaseModel):
    """模型使用分布"""
    name: str
    value: int
