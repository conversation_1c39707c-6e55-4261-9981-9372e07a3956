#!/usr/bin/env python3
"""
验证检测和启动脚本
验证完成后运行此脚本检测是否生效
"""

import subprocess
import time
import requests
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def restart_migpt_container():
    """重启MiGPT容器"""
    log("🔄 重启MiGPT容器...")
    
    try:
        # 停止并删除现有容器
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True)
        
        # 启动新容器
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js",
            "-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json",
            "idootop/mi-gpt:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            log("✅ 容器重启成功")
            return True
        else:
            log(f"❌ 容器启动失败: {result.stderr}")
            return False
    except Exception as e:
        log(f"❌ 重启容器异常: {e}")
        return False

def check_container_status():
    """检查容器状态"""
    log("👀 检查容器启动状态...")
    
    # 等待容器初始化
    time.sleep(15)
    
    try:
        result = subprocess.run(
            ["docker", "logs", "--tail", "20", "xiaoai-llm"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            logs = result.stdout
            print("\n📋 容器日志:")
            print("-" * 50)
            print(logs)
            print("-" * 50)
            
            if "Mi Services 初始化成功" in logs:
                log("🎉 验证生效！MiGPT启动成功")
                return "success"
            elif "触发小米账号异地登录安全验证机制" in logs:
                log("⚠️ 验证尚未生效，仍需要验证")
                return "need_wait"
            elif "小米账号登录失败" in logs:
                log("❌ 登录失败")
                return "failed"
            else:
                log("⚠️ 状态不明确")
                return "unknown"
        else:
            log("❌ 无法获取容器日志")
            return "error"
    except Exception as e:
        log(f"❌ 检查状态失败: {e}")
        return "error"

def update_login_state():
    """更新登录状态文件"""
    log("📤 更新登录状态文件...")
    
    try:
        result = subprocess.run(
            ["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            log("✅ 登录状态文件已更新")
            return True
        else:
            log("⚠️ 无法更新登录状态文件")
            return False
    except Exception as e:
        log(f"❌ 更新登录状态文件失败: {e}")
        return False

def check_backend_status():
    """检查后端API状态"""
    try:
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            log("✅ 后端API正常")
            return data
        else:
            log("❌ 后端API异常")
            return None
    except Exception as e:
        log(f"❌ 后端API连接失败: {e}")
        return None

def main():
    print("🔍 小米账号验证检测和启动工具")
    print("="*60)
    
    log("🎯 检测验证是否生效并启动系统")
    
    # 1. 检查后端状态
    backend_status = check_backend_status()
    if backend_status:
        print(f"📊 当前系统状态: {backend_status}")
    
    # 2. 重启容器测试
    if not restart_migpt_container():
        print("❌ 容器重启失败，请检查Docker服务")
        return
    
    # 3. 检查启动状态
    status = check_container_status()
    
    # 4. 处理结果
    print("\n" + "="*60)
    if status == "success":
        print("🎉 验证已生效！系统启动成功")
        print("✅ 小米账号连接正常")
        print("✅ MiGPT服务运行中")
        
        # 更新登录状态
        update_login_state()
        
        print("\n🚀 系统已完全启动，您现在可以:")
        print("1. 🗣️  对小爱音箱说话，享受AI增强功能")
        print("2. 🌐 访问管理界面: file:///d:/XiaoaiTX/simple-frontend.html")
        print("3. 📊 查看API状态: http://localhost:8000/api/status")
        print("4. 📱 使用验证工具: file:///d:/XiaoaiTX/get_verification_link.html")
        
    elif status == "need_wait":
        print("⏰ 验证尚未生效，需要继续等待")
        print("💡 建议:")
        print("1. 等待10-30分钟后重新运行此脚本")
        print("2. 或者等待1小时后再试")
        print("3. 运行命令: python check_and_start.py")
        
        # 停止容器避免无限重启
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True)
        log("🛑 已停止容器，避免无限重启")
        
    elif status == "failed":
        print("❌ 登录失败")
        print("💡 可能需要:")
        print("1. 重新获取验证链接")
        print("2. 重新完成验证流程")
        print("3. 检查小米账号状态")
        
    else:
        print("⚠️ 状态检测失败或不明确")
        print("💡 建议:")
        print("1. 检查Docker服务是否正常")
        print("2. 查看详细日志: docker logs xiaoai-llm")
        print("3. 稍后重新运行此脚本")
    
    print("="*60)

if __name__ == "__main__":
    main()
