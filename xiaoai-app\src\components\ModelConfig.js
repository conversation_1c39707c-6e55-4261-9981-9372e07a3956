import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Alert,
  InputNumber,
  Tabs,
  Tag,
  message
} from 'antd';
import { 
  ApiOutlined, 
  SaveOutlined, 
  TestOutlined, 
  ReloadOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const ModelConfig = ({ socket }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [config, setConfig] = useState({});
  const [testResults, setTestResults] = useState({});

  const models = [
    {
      key: 'deepseek',
      name: 'DeepSeek',
      description: '性价比高，中文优化',
      color: 'blue',
      fields: ['api_key', 'model', 'base_url', 'max_tokens', 'temperature']
    },
    {
      key: 'wenxin',
      name: '文心一言',
      description: '百度出品，合规性强',
      color: 'purple',
      fields: ['api_key', 'secret_key', 'model', 'max_tokens', 'temperature']
    },
    {
      key: 'qianwen',
      name: '通义千问',
      description: '阿里出品，生态集成',
      color: 'orange',
      fields: ['api_key', 'model', 'max_tokens', 'temperature']
    },
    {
      key: 'gpt4',
      name: 'GPT-4',
      description: 'OpenAI出品，能力最强',
      color: 'green',
      fields: ['api_key', 'model', 'base_url', 'max_tokens', 'temperature']
    }
  ];

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/model-config');
      setConfig(response.data);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async (values) => {
    try {
      setLoading(true);
      await axios.post('/api/model-config', values);
      setConfig(values);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  const testModel = async (modelKey) => {
    try {
      setTestLoading(true);
      const response = await axios.post('/api/test-model', { model: modelKey });
      setTestResults(prev => ({
        ...prev,
        [modelKey]: {
          success: response.data.success,
          message: response.data.message,
          response_time: response.data.response_time,
          cost: response.data.cost
        }
      }));
      
      if (response.data.success) {
        message.success(`${modelKey} 测试成功`);
      } else {
        message.error(`${modelKey} 测试失败: ${response.data.message}`);
      }
    } catch (error) {
      console.error('测试模型失败:', error);
      setTestResults(prev => ({
        ...prev,
        [modelKey]: {
          success: false,
          message: error.response?.data?.message || '测试失败',
          response_time: null,
          cost: null
        }
      }));
      message.error(`${modelKey} 测试失败`);
    } finally {
      setTestLoading(false);
    }
  };

  const renderModelConfig = (model) => {
    const testResult = testResults[model.key];
    
    return (
      <Card 
        key={model.key}
        title={
          <Space>
            <Tag color={model.color}>{model.name}</Tag>
            <span style={{ fontSize: '14px', color: '#666' }}>
              {model.description}
            </span>
          </Space>
        }
        extra={
          <Space>
            <Button
              size="small"
              icon={<TestOutlined />}
              onClick={() => testModel(model.key)}
              loading={testLoading}
            >
              测试
            </Button>
            {testResult && (
              <Tag color={testResult.success ? 'green' : 'red'}>
                {testResult.success ? '正常' : '异常'}
              </Tag>
            )}
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        {/* 测试结果显示 */}
        {testResult && (
          <Alert
            message={testResult.success ? '测试成功' : '测试失败'}
            description={
              <div>
                <div>{testResult.message}</div>
                {testResult.response_time && (
                  <div>响应时间: {testResult.response_time}ms</div>
                )}
                {testResult.cost && (
                  <div>测试成本: ${testResult.cost.toFixed(6)}</div>
                )}
              </div>
            }
            type={testResult.success ? 'success' : 'error'}
            style={{ marginBottom: 16 }}
          />
        )}

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
          {model.fields.includes('api_key') && (
            <Form.Item
              name={[model.key, 'api_key']}
              label="API Key"
              rules={[{ required: true, message: '请输入API Key' }]}
            >
              <Input.Password placeholder="请输入API Key" />
            </Form.Item>
          )}

          {model.fields.includes('secret_key') && (
            <Form.Item
              name={[model.key, 'secret_key']}
              label="Secret Key"
              rules={[{ required: true, message: '请输入Secret Key' }]}
            >
              <Input.Password placeholder="请输入Secret Key" />
            </Form.Item>
          )}

          {model.fields.includes('model') && (
            <Form.Item
              name={[model.key, 'model']}
              label="模型名称"
              rules={[{ required: true, message: '请输入模型名称' }]}
            >
              <Input placeholder="请输入模型名称" />
            </Form.Item>
          )}

          {model.fields.includes('base_url') && (
            <Form.Item
              name={[model.key, 'base_url']}
              label="API地址"
            >
              <Input placeholder="请输入API地址" />
            </Form.Item>
          )}

          {model.fields.includes('max_tokens') && (
            <Form.Item
              name={[model.key, 'max_tokens']}
              label="最大Token数"
            >
              <InputNumber 
                min={1} 
                max={4000} 
                placeholder="1000"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}

          {model.fields.includes('temperature') && (
            <Form.Item
              name={[model.key, 'temperature']}
              label="温度参数"
            >
              <InputNumber 
                min={0} 
                max={2} 
                step={0.1} 
                placeholder="0.7"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
        </div>

        <Form.Item
          name={[model.key, 'enabled']}
          valuePropName="checked"
        >
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>
      </Card>
    );
  };

  return (
    <div className="model-config fade-in">
      <Card 
        title={
          <Space>
            <ApiOutlined />
            AI模型配置
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadConfig}>
              重新加载
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={() => form.submit()}
              loading={loading}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveConfig}
          initialValues={{
            routing: {
              default_model: 'deepseek',
              fallback_model: 'wenxin',
              enable_smart_routing: true
            },
            cost_control: {
              daily_limit: 5.0,
              warning_threshold: 0.8
            }
          }}
        >
          <Tabs defaultActiveKey="models">
            <TabPane tab="模型配置" key="models">
              <Alert
                message="模型配置说明"
                description="配置各个AI大模型的API密钥和参数。建议至少配置一个主力模型和一个备用模型。"
                type="info"
                style={{ marginBottom: 24 }}
              />
              
              {models.map(renderModelConfig)}
            </TabPane>

            <TabPane tab="路由配置" key="routing">
              <Card title="智能路由设置" style={{ marginBottom: 16 }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
                  <Form.Item
                    name={['routing', 'default_model']}
                    label="默认模型"
                    rules={[{ required: true, message: '请选择默认模型' }]}
                  >
                    <Select placeholder="请选择默认模型">
                      {models.map(model => (
                        <Option key={model.key} value={model.key}>
                          <Tag color={model.color}>{model.name}</Tag>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name={['routing', 'fallback_model']}
                    label="备用模型"
                    rules={[{ required: true, message: '请选择备用模型' }]}
                  >
                    <Select placeholder="请选择备用模型">
                      {models.map(model => (
                        <Option key={model.key} value={model.key}>
                          <Tag color={model.color}>{model.name}</Tag>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <Form.Item
                  name={['routing', 'enable_smart_routing']}
                  valuePropName="checked"
                >
                  <Switch checkedChildren="启用智能路由" unCheckedChildren="禁用智能路由" />
                </Form.Item>
              </Card>
            </TabPane>

            <TabPane tab="成本控制" key="cost">
              <Card title="成本控制设置" style={{ marginBottom: 16 }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
                  <Form.Item
                    name={['cost_control', 'daily_limit']}
                    label="每日成本限制 (USD)"
                  >
                    <InputNumber 
                      min={0} 
                      step={0.1} 
                      placeholder="5.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    name={['cost_control', 'warning_threshold']}
                    label="预警阈值 (百分比)"
                  >
                    <InputNumber 
                      min={0} 
                      max={1} 
                      step={0.1} 
                      placeholder="0.8"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>
              </Card>
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </div>
  );
};

export default ModelConfig;
