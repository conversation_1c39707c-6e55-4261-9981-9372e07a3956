# 小爱同学AI大模型升级项目依赖包

# 核心框架
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# AI大模型相关
openai==1.3.0
anthropic==0.7.0
requests==2.31.0
httpx==0.25.0

# 异步处理
asyncio==3.4.3
aiohttp==3.9.0
aiofiles==23.2.0

# 数据处理
pandas==2.1.0
numpy==1.24.0
python-json-logger==2.0.7

# 数据库
redis==5.0.1
pymongo==4.6.0
sqlalchemy==2.0.23

# 语音处理
librosa==0.10.1
soundfile==1.0.0
pydub==0.25.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1
configparser==6.0.0

# 安全相关
cryptography==41.0.7
passlib==1.7.4
python-jose==3.3.0

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2

# 测试
pytest==7.4.0
pytest-asyncio==0.21.0
pytest-mock==3.12.0
httpx==0.25.0

# 开发工具
black==23.11.0
flake8==6.1.0
mypy==1.7.0
pre-commit==3.6.0

# 其他工具
click==8.1.7
rich==13.7.0
typer==0.9.0
schedule==1.2.0
