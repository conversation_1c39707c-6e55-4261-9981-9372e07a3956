#!/usr/bin/env python3
"""
简单可靠的验证检测脚本
避免复杂逻辑，直接检测关键状态
"""

import subprocess
import time
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_verification_status():
    """简单直接的验证状态检查"""
    log("🔍 开始检查验证状态...")
    
    try:
        # 1. 停止现有容器
        log("🛑 停止现有容器...")
        subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
        subprocess.run(["docker", "rm", "xiaoai-llm"], capture_output=True, timeout=10)
        
        # 2. 启动新容器
        log("🚀 启动新容器...")
        cmd = [
            "docker", "run", "-d",
            "--name", "xiaoai-llm",
            "--env-file", "mi-gpt/.env",
            "-v", "d:/XiaoaiTX/mi-gpt/.migpt.js:/app/.migpt.js",
            "-v", "d:/XiaoaiTX/mi-gpt/.mi.json:/app/.mi.json",
            "idootop/mi-gpt:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            log(f"❌ 容器启动失败: {result.stderr}")
            return False
        
        log("✅ 容器启动成功，等待初始化...")
        
        # 3. 等待初始化
        time.sleep(20)
        
        # 4. 获取日志
        log("📋 获取容器日志...")
        log_result = subprocess.run(
            ["docker", "logs", "--tail", "20", "xiaoai-llm"],
            capture_output=True, text=True, timeout=15
        )
        
        if log_result.returncode != 0:
            log("❌ 无法获取容器日志")
            return False
        
        # 5. 分析日志（简单安全的方式）
        logs = log_result.stdout
        if not logs:
            logs = ""
        
        log("📋 容器日志内容:")
        print("-" * 50)
        print(logs)
        print("-" * 50)
        
        # 6. 简单判断
        if logs and "Mi Services 初始化成功" in logs:
            log("🎉 验证成功！MiGPT启动完成")
            return True
        elif logs and ("小米账号登录失败" in logs or "初始化 Mi Services 失败" in logs):
            log("❌ 验证仍未生效")
            return False
        else:
            log("⚠️ 状态不明确")
            return False
            
    except Exception as e:
        log(f"❌ 检查过程出错: {e}")
        return False

def main():
    print("🔍 简单可靠的验证检测")
    print("="*50)
    
    log("🎯 开始检测验证是否生效...")
    
    success = check_verification_status()
    
    print("\n" + "="*50)
    if success:
        print("🎉 验证检测结果: 成功")
        print("✅ 小米账号验证已生效")
        print("✅ MiGPT服务正常运行")
        print("🚀 系统已完全启动")
        
        # 更新登录状态
        try:
            subprocess.run(["docker", "cp", "xiaoai-llm:/app/.mi.json", "mi-gpt/.mi.json"], 
                         capture_output=True, timeout=10)
            log("✅ 登录状态文件已更新")
        except:
            log("⚠️ 登录状态文件更新失败")
        
    else:
        print("❌ 验证检测结果: 失败")
        print("⚠️ 验证仍未生效")
        print("💡 可能原因:")
        print("1. 小米验证需要更长时间")
        print("2. 小米安全机制过于严格")
        print("3. 需要重新验证")
        
        # 停止容器避免无限重启
        try:
            subprocess.run(["docker", "stop", "xiaoai-llm"], capture_output=True, timeout=10)
            log("🛑 已停止容器")
        except:
            pass
    
    print("="*50)
    return success

if __name__ == "__main__":
    main()
