#!/usr/bin/env python3
"""
设备配置修复工具
修复MiGPT配置中的设备ID问题
"""

import os
import shutil
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def backup_config():
    """备份原配置文件"""
    config_file = "mi-gpt\\.migpt.js"
    backup_file = f"mi-gpt\\.migpt.js.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        shutil.copy2(config_file, backup_file)
        log(f"✅ 配置文件已备份: {backup_file}")
        return True
    except Exception as e:
        log(f"❌ 备份失败: {e}")
        return False

def suggest_device_names():
    """建议设备名称"""
    log("💡 建议的设备名称配置:")
    
    suggestions = [
        "1. 使用英文名称: 'xiaoai-speaker'",
        "2. 使用简单名称: 'xiaomi-01'", 
        "3. 使用默认名称: 'Mi AI Speaker'",
        "4. 使用设备型号: 'LX05' (您的设备型号)",
        "5. 使用纯数字ID: 如果知道设备DID"
    ]
    
    for suggestion in suggestions:
        log(f"  {suggestion}")

def create_fixed_configs():
    """创建修复后的配置文件选项"""
    
    # 读取原配置
    try:
        with open("mi-gpt\\.migpt.js", "r", encoding="utf-8") as f:
            original_content = f.read()
    except Exception as e:
        log(f"❌ 读取原配置失败: {e}")
        return False
    
    # 创建多个修复选项
    fix_options = [
        ("xiaoai-speaker", "使用英文名称"),
        ("xiaomi-01", "使用简单标识"),
        ("Mi AI Speaker", "使用默认名称"),
        ("LX05", "使用设备型号")
    ]
    
    for device_name, description in fix_options:
        # 替换设备ID
        fixed_content = original_content.replace(
            'did: "老家小爱"',
            f'did: "{device_name}"'
        )
        
        # 保存修复后的配置
        output_file = f"mi-gpt\\.migpt.js.fixed_{device_name.replace(' ', '_').replace('-', '_')}"
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(fixed_content)
            log(f"✅ 创建修复配置: {output_file} ({description})")
        except Exception as e:
            log(f"❌ 创建配置失败: {e}")
    
    return True

def apply_recommended_fix():
    """应用推荐的修复"""
    log("🔧 应用推荐修复 (使用英文设备名)...")
    
    try:
        # 读取原配置
        with open("mi-gpt\\.migpt.js", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 应用修复
        fixed_content = content.replace(
            'did: "老家小爱"',
            'did: "xiaoai-speaker"'
        )
        
        # 保存修复后的配置
        with open("mi-gpt\\.migpt.js", "w", encoding="utf-8") as f:
            f.write(fixed_content)
        
        log("✅ 配置文件已修复")
        log("📝 设备ID已更改: '老家小爱' -> 'xiaoai-speaker'")
        return True
        
    except Exception as e:
        log(f"❌ 应用修复失败: {e}")
        return False

def update_container_config():
    """更新容器配置"""
    log("🐳 更新容器配置...")
    
    import subprocess
    
    try:
        # 停止容器
        log("🛑 停止容器...")
        subprocess.run(["docker", "stop", "xiaoai-llm"], check=True, capture_output=True)
        
        # 重新启动容器以加载新配置
        log("🚀 重新启动容器...")
        subprocess.run(["docker", "start", "xiaoai-llm"], check=True, capture_output=True)
        
        log("✅ 容器配置已更新")
        return True
        
    except subprocess.CalledProcessError as e:
        log(f"❌ 更新容器失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    log("🔍 验证修复结果...")
    
    try:
        # 检查配置文件
        with open("mi-gpt\\.migpt.js", "r", encoding="utf-8") as f:
            content = f.read()
        
        if 'did: "老家小爱"' in content:
            log("❌ 配置未修复，仍包含中文设备名")
            return False
        elif 'did: "xiaoai-speaker"' in content:
            log("✅ 配置已修复，使用英文设备名")
            return True
        else:
            log("⚠️ 配置已修改，但使用了其他设备名")
            return True
            
    except Exception as e:
        log(f"❌ 验证失败: {e}")
        return False

def main():
    print("🔧 MiGPT设备配置修复工具")
    print("="*50)
    
    # 1. 备份原配置
    if not backup_config():
        print("❌ 备份失败，停止修复")
        return
    
    # 2. 显示建议
    print()
    suggest_device_names()
    
    # 3. 创建修复选项
    print("\n" + "="*50)
    log("🔧 创建修复配置选项...")
    create_fixed_configs()
    
    # 4. 应用推荐修复
    print("\n" + "="*50)
    if apply_recommended_fix():
        # 5. 更新容器配置
        print()
        if update_container_config():
            # 6. 验证修复
            print()
            if verify_fix():
                print("\n" + "="*50)
                log("🎉 设备配置修复完成！")
                log("💡 下一步:")
                log("  1. 完成小米账号验证: python smart_verification_helper.py")
                log("  2. 监控容器状态: python migpt_manager.py monitor")
                log("  3. 检查容器日志: docker logs --tail 20 xiaoai-llm")
            else:
                log("⚠️ 修复验证失败，请手动检查配置")
        else:
            log("⚠️ 容器更新失败，请手动重启容器")
    else:
        log("❌ 配置修复失败")
    
    print("\n📋 可用的修复配置文件:")
    import glob
    for config_file in glob.glob("mi-gpt\\.migpt.js.fixed_*"):
        print(f"  {config_file}")

if __name__ == "__main__":
    main()
