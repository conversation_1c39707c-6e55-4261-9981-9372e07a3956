# 小爱同学AI大模型接入开发实施指南

## 项目结构

```
xiaoai-llm-upgrade/
├── src/
│   ├── llm/                    # 大模型接入层
│   │   ├── providers/          # 各大模型提供商
│   │   ├── router.py          # 智能路由
│   │   └── manager.py         # 模型管理
│   ├── dialogue/              # 对话管理
│   │   ├── context.py         # 上下文管理
│   │   ├── memory.py          # 记忆管理
│   │   └── session.py         # 会话管理
│   ├── prompt/                # Prompt工程
│   │   ├── templates/         # 模板库
│   │   ├── optimizer.py       # 优化器
│   │   └── evaluator.py       # 效果评估
│   ├── security/              # 安全模块
│   │   ├── content_filter.py  # 内容过滤
│   │   ├── rate_limiter.py    # 频率限制
│   │   └── privacy.py         # 隐私保护
│   └── integration/           # 小米生态集成
│       ├── iot_control.py     # IoT设备控制
│       ├── service_api.py     # 服务API
│       └── user_profile.py    # 用户画像
├── tests/                     # 测试代码
├── docs/                      # 文档
├── config/                    # 配置文件
└── requirements.txt           # 依赖包
```

## 核心模块开发

### 1. 大模型接入层

#### 1.1 统一接口设计
```python
# src/llm/base.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator

class LLMProvider(ABC):
    """大模型提供商基类"""
    
    @abstractmethod
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> Dict[str, Any]:
        """聊天补全接口"""
        pass
    
    @abstractmethod
    async def stream_chat(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        pass
    
    @abstractmethod
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算调用成本"""
        pass
```

#### 1.2 文心一言接入
```python
# src/llm/providers/wenxin.py
import aiohttp
from ..base import LLMProvider

class WenxinProvider(LLMProvider):
    def __init__(self, api_key: str, secret_key: str):
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
    
    async def get_access_token(self):
        """获取访问令牌"""
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, params=params) as resp:
                data = await resp.json()
                self.access_token = data.get("access_token")
    
    async def chat_completion(self, messages: List[Dict[str, str]], **kwargs):
        if not self.access_token:
            await self.get_access_token()
        
        url = f"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token={self.access_token}"
        
        payload = {
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000)
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as resp:
                return await resp.json()
```

### 2. 智能路由系统

```python
# src/llm/router.py
from enum import Enum
from typing import List, Dict
import re

class IntentType(Enum):
    SIMPLE_QA = "simple_qa"          # 简单问答，用传统NLU
    COMPLEX_DIALOGUE = "complex"      # 复杂对话，用大模型
    IOT_CONTROL = "iot_control"      # IoT控制，用传统方式
    CREATIVE = "creative"            # 创意生成，用大模型
    KNOWLEDGE = "knowledge"          # 知识问答，用大模型

class IntentRouter:
    def __init__(self):
        self.simple_patterns = [
            r"今天天气",
            r"现在几点",
            r"设置闹钟",
            r"播放音乐"
        ]
        
        self.iot_patterns = [
            r"打开|关闭.*灯",
            r"调节.*温度",
            r"启动.*模式"
        ]
    
    def route_intent(self, user_input: str) -> IntentType:
        """根据用户输入判断意图类型"""
        
        # 检查是否为简单问答
        for pattern in self.simple_patterns:
            if re.search(pattern, user_input):
                return IntentType.SIMPLE_QA
        
        # 检查是否为IoT控制
        for pattern in self.iot_patterns:
            if re.search(pattern, user_input):
                return IntentType.IOT_CONTROL
        
        # 检查是否为创意生成
        creative_keywords = ["写", "创作", "编", "想象", "设计"]
        if any(keyword in user_input for keyword in creative_keywords):
            return IntentType.CREATIVE
        
        # 默认使用大模型处理
        return IntentType.COMPLEX_DIALOGUE
```

### 3. 对话上下文管理

```python
# src/dialogue/context.py
from typing import List, Dict, Optional
import json
import time

class DialogueContext:
    def __init__(self, user_id: str, max_history: int = 10):
        self.user_id = user_id
        self.max_history = max_history
        self.messages: List[Dict] = []
        self.user_profile = {}
        self.session_start = time.time()
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict] = None):
        """添加消息到上下文"""
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        self.messages.append(message)
        
        # 保持历史长度限制
        if len(self.messages) > self.max_history * 2:  # 用户+助手消息对
            self.messages = self.messages[-self.max_history * 2:]
    
    def get_context_for_llm(self) -> List[Dict[str, str]]:
        """获取适合大模型的上下文格式"""
        llm_messages = []
        
        # 添加系统提示
        system_prompt = self._build_system_prompt()
        llm_messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史对话
        for msg in self.messages[-10:]:  # 最近10条消息
            llm_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        return llm_messages
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        base_prompt = """你是小爱同学，小米公司开发的智能助手。你的特点是：
1. 友好、热情、乐于助人
2. 了解小米生态产品和服务
3. 能够控制智能家居设备
4. 回答要简洁明了，适合语音交互
5. 保护用户隐私，不泄露个人信息"""
        
        # 根据用户画像个性化
        if self.user_profile.get("age_group") == "young":
            base_prompt += "\n6. 语言风格活泼，可以使用一些网络用语"
        
        return base_prompt
```

### 4. Prompt工程优化

```python
# src/prompt/templates.py
from typing import Dict, List
import json

class PromptTemplate:
    """Prompt模板管理"""
    
    TEMPLATES = {
        "general_chat": """你是小爱同学，请根据用户的问题给出有用的回答。
用户问题：{user_input}
请回答：""",
        
        "iot_control": """你是小爱同学，用户想要控制智能设备。
当前可控制的设备：{available_devices}
用户指令：{user_input}
请生成控制指令：""",
        
        "knowledge_qa": """你是小爱同学，请回答用户的知识性问题。
问题：{user_input}
要求：
1. 回答准确、简洁
2. 如果不确定，请说明
3. 适合语音播报
回答：""",
        
        "creative": """你是小爱同学，请协助用户进行创意工作。
用户需求：{user_input}
请提供创意内容："""
    }
    
    @classmethod
    def get_template(cls, template_name: str) -> str:
        return cls.TEMPLATES.get(template_name, cls.TEMPLATES["general_chat"])
    
    @classmethod
    def format_prompt(cls, template_name: str, **kwargs) -> str:
        template = cls.get_template(template_name)
        return template.format(**kwargs)
```

## 部署配置

### 1. 环境配置

```yaml
# config/production.yaml
llm:
  providers:
    wenxin:
      api_key: "${WENXIN_API_KEY}"
      secret_key: "${WENXIN_SECRET_KEY}"
      model: "ernie-bot-turbo"
    
    qianwen:
      api_key: "${QIANWEN_API_KEY}"
      model: "qwen-turbo"
  
  router:
    default_provider: "wenxin"
    fallback_provider: "qianwen"
    max_retries: 3

dialogue:
  max_context_length: 10
  session_timeout: 1800  # 30分钟

security:
  content_filter:
    enabled: true
    sensitive_words_file: "config/sensitive_words.txt"
  
  rate_limit:
    requests_per_minute: 60
    requests_per_hour: 1000

cache:
  redis_url: "${REDIS_URL}"
  ttl: 3600  # 1小时
```

### 2. Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY config/ ./config/

EXPOSE 8000

CMD ["python", "-m", "src.main"]
```

## 测试策略

### 1. 单元测试
```python
# tests/test_llm_provider.py
import pytest
from src.llm.providers.wenxin import WenxinProvider

@pytest.mark.asyncio
async def test_wenxin_chat_completion():
    provider = WenxinProvider("test_key", "test_secret")
    
    messages = [
        {"role": "user", "content": "你好"}
    ]
    
    # Mock API响应
    with patch('aiohttp.ClientSession.post') as mock_post:
        mock_response = {
            "result": "你好！我是小爱同学，很高兴为您服务！",
            "usage": {"total_tokens": 20}
        }
        mock_post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        result = await provider.chat_completion(messages)
        assert "result" in result
```

### 2. 集成测试
```python
# tests/test_integration.py
import pytest
from src.main import XiaoaiLLMService

@pytest.mark.asyncio
async def test_end_to_end_dialogue():
    service = XiaoaiLLMService()
    
    # 测试简单对话
    response = await service.process_user_input(
        user_id="test_user",
        input_text="今天天气怎么样？"
    )
    
    assert response["success"] is True
    assert "weather" in response["content"].lower()
```

## 监控和运维

### 1. 关键指标监控
- API调用成功率
- 平均响应时间
- 成本消耗
- 用户满意度

### 2. 日志记录
```python
# src/utils/logger.py
import logging
import json
from datetime import datetime

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_llm_call(self, provider: str, input_tokens: int, 
                     output_tokens: int, cost: float, latency: float):
        """记录大模型调用日志"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "event": "llm_call",
            "provider": provider,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cost": cost,
            "latency": latency
        }
        self.logger.info(json.dumps(log_data))
```

---

*本指南提供了完整的开发框架，可根据实际需求调整实现细节。*
