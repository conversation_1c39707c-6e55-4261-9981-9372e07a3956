"""
小米服务
"""

import asyncio
from typing import Dict, Any, List
from loguru import logger
from ..database import ConfigManager

class XiaomiService:
    """小米服务管理器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
    
    async def get_config(self) -> Dict[str, Any]:
        """获取小米配置"""
        try:
            config = await self.config_manager.get_config("xiaomi_config", {})
            return config
        except Exception as e:
            logger.error(f"获取小米配置失败: {e}")
            return {}
    
    async def save_config(self, config: Dict[str, Any]) -> bool:
        """保存小米配置"""
        try:
            return await self.config_manager.set_config("xiaomi_config", config)
        except Exception as e:
            logger.error(f"保存小米配置失败: {e}")
            return False
    
    async def get_login_status(self) -> str:
        """获取登录状态"""
        try:
            import subprocess

            # 检查配置
            config = await self.get_config()
            if not config.get("userId") or not config.get("password"):
                return "not_configured"

            # 检查MiGPT容器日志获取真实状态
            try:
                result = subprocess.run(
                    ["docker", "logs", "--tail", "20", "xiaoai-llm"],
                    capture_output=True, text=True, timeout=10,
                    encoding='utf-8', errors='ignore'
                )

                logs = result.stdout

                # 检查最严重的错误状态（优先级从高到低）

                # 1. 检查初始化失败（最严重）
                if "初始化 Mi Services 失败" in logs:
                    return "init_failed"

                # 2. 检查登录失败
                if "小米账号登录失败" in logs:
                    return "login_failed"

                # 3. 检查需要验证
                if "触发小米账号异地登录安全验证机制" in logs:
                    return "need_verification"

                # 4. 检查成功状态
                if "✅" in logs and ("登录成功" in logs or "连接成功" in logs or "Mi Services 初始化成功" in logs):
                    return "connected"

                # 5. 如果配置存在但没有明确的状态信息
                return "configured"

            except Exception as e:
                logger.warning(f"检查MiGPT日志失败: {e}")
                return "configured"

        except Exception as e:
            logger.error(f"获取登录状态失败: {e}")
            return "error"
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试小米连接"""
        try:
            config = await self.get_config()
            
            if not config.get("userId") or not config.get("password"):
                return {
                    "success": False,
                    "message": "请先配置小米账号信息"
                }
            
            # 模拟连接测试
            await asyncio.sleep(2)
            
            return {
                "success": True,
                "message": "连接成功",
                "account_info": {
                    "userId": config.get("userId"),
                    "nickname": "小米用户",
                    "phone": "189****0519",
                    "email": "<EMAIL>",
                    "loginTime": "2024-01-01 12:00:00"
                }
            }
        except Exception as e:
            logger.error(f"测试小米连接失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }
    
    async def get_devices(self) -> List[Dict[str, Any]]:
        """获取设备列表"""
        try:
            # 模拟设备列表
            return [
                {
                    "did": "*********",
                    "name": "小爱音箱Pro",
                    "model": "LX05",
                    "type": "speaker",
                    "online": True,
                    "ip": "*************",
                    "mac": "AA:BB:CC:DD:EE:FF",
                    "firmware_version": "1.2.3"
                },
                {
                    "did": "987654321",
                    "name": "小爱音箱Play",
                    "model": "LX01",
                    "type": "speaker",
                    "online": False,
                    "ip": "*************",
                    "mac": "FF:EE:DD:CC:BB:AA",
                    "firmware_version": "1.1.0"
                }
            ]
        except Exception as e:
            logger.error(f"获取设备列表失败: {e}")
            return []
