"""
配置管理模块
"""
import os
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from enum import Enum


class Environment(str, Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"


class LLMProvider(str, Enum):
    """大模型提供商"""
    WENXIN = "wenxin"
    QIANWEN = "qianwen"
    GPT4 = "gpt4"
    DOUBAO = "doubao"
    CLAUDE = "claude"


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = "小爱同学AI大模型升级"
    app_version: str = "1.0.0"
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    
    # 大模型配置
    default_llm_provider: LLMProvider = LLMProvider.WENXIN
    fallback_llm_provider: LLMProvider = LLMProvider.QIANWEN
    max_retries: int = 3
    request_timeout: int = 30
    
    # 文心一言配置
    wenxin_api_key: Optional[str] = Field(None, env="WENXIN_API_KEY")
    wenxin_secret_key: Optional[str] = Field(None, env="WENXIN_SECRET_KEY")
    wenxin_model: str = "ernie-bot-turbo"
    
    # 通义千问配置
    qianwen_api_key: Optional[str] = Field(None, env="QIANWEN_API_KEY")
    qianwen_model: str = "qwen-turbo"
    
    # GPT配置
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_model: str = "gpt-3.5-turbo"
    openai_base_url: Optional[str] = None
    
    # 豆包配置
    doubao_api_key: Optional[str] = Field(None, env="DOUBAO_API_KEY")
    doubao_model: str = "doubao-pro-4k"
    
    # Claude配置
    claude_api_key: Optional[str] = Field(None, env="CLAUDE_API_KEY")
    claude_model: str = "claude-3-haiku-20240307"
    
    # 对话管理配置
    max_context_length: int = 10
    session_timeout: int = 1800  # 30分钟
    max_tokens: int = 1000
    temperature: float = 0.7
    
    # Redis配置
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    
    # 数据库配置
    mongodb_url: str = Field("mongodb://localhost:27017", env="MONGODB_URL")
    database_name: str = "xiaoai_llm"
    
    # 安全配置
    secret_key: str = Field("your-secret-key-here", env="SECRET_KEY")
    access_token_expire_minutes: int = 30
    
    # 内容安全配置
    enable_content_filter: bool = True
    sensitive_words_file: str = "config/sensitive_words.txt"
    
    # 频率限制配置
    rate_limit_requests_per_minute: int = 60
    rate_limit_requests_per_hour: int = 1000
    
    # 缓存配置
    cache_ttl: int = 3600  # 1小时
    enable_cache: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/xiaoai.log"
    log_rotation: str = "1 day"
    log_retention: str = "30 days"
    
    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # 小米生态配置
    xiaomi_iot_api_url: str = "https://api.io.mi.com"
    xiaomi_iot_api_key: Optional[str] = Field(None, env="XIAOMI_IOT_API_KEY")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_llm_config(provider: LLMProvider) -> Dict[str, Any]:
    """获取指定大模型提供商的配置"""
    configs = {
        LLMProvider.WENXIN: {
            "api_key": settings.wenxin_api_key,
            "secret_key": settings.wenxin_secret_key,
            "model": settings.wenxin_model,
            "base_url": "https://aip.baidubce.com"
        },
        LLMProvider.QIANWEN: {
            "api_key": settings.qianwen_api_key,
            "model": settings.qianwen_model,
            "base_url": "https://dashscope.aliyuncs.com"
        },
        LLMProvider.GPT4: {
            "api_key": settings.openai_api_key,
            "model": settings.openai_model,
            "base_url": settings.openai_base_url or "https://api.openai.com"
        },
        LLMProvider.DOUBAO: {
            "api_key": settings.doubao_api_key,
            "model": settings.doubao_model,
            "base_url": "https://ark.cn-beijing.volces.com"
        },
        LLMProvider.CLAUDE: {
            "api_key": settings.claude_api_key,
            "model": settings.claude_model,
            "base_url": "https://api.anthropic.com"
        }
    }
    return configs.get(provider, {})


def validate_config() -> bool:
    """验证配置是否完整"""
    required_configs = [
        (settings.default_llm_provider, "默认大模型提供商"),
        (settings.secret_key, "密钥"),
        (settings.redis_url, "Redis连接"),
        (settings.mongodb_url, "MongoDB连接")
    ]
    
    for config_value, config_name in required_configs:
        if not config_value:
            print(f"❌ 缺少必要配置: {config_name}")
            return False
    
    # 验证默认大模型配置
    default_config = get_llm_config(settings.default_llm_provider)
    if not default_config.get("api_key"):
        print(f"❌ 缺少默认大模型({settings.default_llm_provider})的API密钥")
        return False
    
    print("✅ 配置验证通过")
    return True


if __name__ == "__main__":
    print("🔧 配置信息:")
    print(f"  应用名称: {settings.app_name}")
    print(f"  版本: {settings.app_version}")
    print(f"  环境: {settings.environment}")
    print(f"  默认大模型: {settings.default_llm_provider}")
    print(f"  备用大模型: {settings.fallback_llm_provider}")
    print()
    validate_config()
