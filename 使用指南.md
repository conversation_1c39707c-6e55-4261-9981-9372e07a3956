# 小爱同学AI大模型升级使用指南

## 🚀 快速开始

### 方式一：一键部署（推荐）

```bash
# 1. 下载部署脚本
curl -O https://raw.githubusercontent.com/your-repo/xiaoai-llm-upgrade/main/deploy.sh

# 2. 给脚本执行权限
chmod +x deploy.sh

# 3. 运行部署脚本
./deploy.sh
```

### 方式二：手动部署

#### 原版部署（基于您的方案）

```bash
# 1. 下载MiGPT项目
git clone https://github.com/idootop/mi-gpt.git
cd mi-gpt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入您的API Key

# 3. 配置MiGPT
cp .migpt.example.js .migpt.js
# 编辑 .migpt.js 文件，填入小米账号信息

# 4. 运行Docker容器
docker run -d \
  --env-file .env \
  -v $(pwd)/.migpt.js:/app/.migpt.js \
  --name xiaoai-llm \
  idootop/mi-gpt:latest
```

#### 增强版部署（多模型支持）

```bash
# 1. 使用增强版配置
cp .env.enhanced .env
cp .migpt.enhanced.js .migpt.js

# 2. 编辑配置文件
# 在 .env 中填入多个模型的API Key
# 在 .migpt.js 中填入小米账号信息

# 3. 使用Docker Compose部署
docker-compose -f docker-compose.enhanced.yml up -d
```

## 📋 配置说明

### 必填配置

#### 1. API密钥配置（.env文件）

```bash
# DeepSeek（主力模型）
OPENAI_API_KEY=sk-xxxxxx
OPENAI_BASE_URL=https://cn.gptapi.asia/v1

# 文心一言（备用模型）
WENXIN_API_KEY=your_wenxin_api_key
WENXIN_SECRET_KEY=your_wenxin_secret_key

# 其他模型（可选）
QIANWEN_API_KEY=your_qianwen_api_key
GPT4_API_KEY=your_gpt4_api_key
```

#### 2. 小米账号配置（.migpt.js文件）

```javascript
speaker: {
  userId: "*********",      // 您的小米ID
  password: "123456",       // 您的小米账号密码
  did: "小爱音箱Pro",       // 您的设备名称
  // ... 其他配置
}
```

### 可选配置

#### 智能路由配置

```javascript
// 在 .migpt.enhanced.js 中
routing: {
  simple: {
    keywords: ["天气", "时间", "音乐"],
    model: "default"  // 使用DeepSeek
  },
  complex: {
    keywords: ["分析", "写作", "创作"],
    model: "premium"  // 使用GPT-4
  }
}
```

#### 成本控制配置

```javascript
costControl: {
  dailyLimit: 5.0,           // 每日成本限制（美元）
  hourlyRequestLimit: 100,   // 每小时请求限制
  warningThreshold: 0.8      // 80%时预警
}
```

## 🎯 使用示例

### 基础对话

```
用户: "小爱同学，你好"
小爱: "你好！我是傻妞，现在拥有更强大的AI能力了！"

用户: "小爱同学，今天天气怎么样？"
小爱: "今天天气晴朗，温度25度，适合出行。"
```

### 知识问答

```
用户: "小爱同学，什么是人工智能？"
小爱: "人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统..."

用户: "小爱同学，树上7只鸟，猎人打下2只，还剩下几只？"
小爱: "这是一个经典的逻辑题。如果猎人开枪打鸟，其他鸟会被枪声吓跑，所以树上可能一只鸟都不剩了。"
```

### 创意生成

```
用户: "小爱同学，帮我写一首关于春天的诗"
小爱: "好的，我来为您创作一首春天的诗：

春风轻抚柳絮飞，
桃花朵朵映晨辉。
燕子归来筑新巢，
万物复苏展生机。"
```

### 智能家居控制

```
用户: "小爱同学，打开客厅的灯"
小爱: "好的，客厅的灯已经为您打开了。"

用户: "小爱同学，把空调温度调到26度"
小爱: "已经将空调温度调节到26度，请稍等片刻。"
```

## 📊 监控和管理

### 查看服务状态

```bash
# 查看容器状态
docker ps

# 查看实时日志
docker logs -f xiaoai-llm

# 查看最近日志
docker logs --tail 50 xiaoai-llm
```

### 增强版监控（仅增强版）

访问监控面板：
- **Grafana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090

监控指标：
- 模型使用情况
- API调用成功率
- 响应时间统计
- 成本消耗分析

### 成本统计

```bash
# 查看成本统计（增强版）
curl http://localhost:3000/api/stats
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 检查日志
docker logs xiaoai-llm

# 常见原因：
# - API Key配置错误
# - 小米账号信息错误
# - 网络连接问题
```

#### 2. 小爱同学无响应

```bash
# 检查设备连接
# 1. 确认设备名称正确
# 2. 确认账号密码正确
# 3. 检查网络连接

# 重启服务
docker restart xiaoai-llm
```

#### 3. API调用失败

```bash
# 检查API Key是否有效
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://cn.gptapi.asia/v1/models

# 检查余额是否充足
```

### 调试模式

```javascript
// 在 .migpt.js 中启用调试
speaker: {
  debug: true,
  enableTrace: true,
  // ... 其他配置
}
```

## 🔄 升级和维护

### 更新服务

```bash
# 原版更新
docker pull idootop/mi-gpt:latest
docker stop xiaoai-llm
docker rm xiaoai-llm
# 重新运行容器

# 增强版更新
docker-compose -f docker-compose.enhanced.yml pull
docker-compose -f docker-compose.enhanced.yml up -d
```

### 备份配置

```bash
# 备份重要配置文件
cp .env .env.backup
cp .migpt.js .migpt.js.backup

# 增强版自动备份（每日）
# 备份文件位置：./backups/
```

### 数据清理

```bash
# 清理日志文件
docker exec xiaoai-llm sh -c "echo '' > /app/logs/app.log"

# 清理Docker镜像
docker system prune -f
```

## 📈 性能优化

### 响应速度优化

1. **启用缓存**（增强版）
   ```javascript
   cache: {
     enabled: true,
     ttl: 3600  // 1小时
   }
   ```

2. **调整模型参数**
   ```javascript
   models: {
     default: {
       maxTokens: 500,    // 减少token数量
       temperature: 0.5   // 降低随机性
     }
   }
   ```

### 成本优化

1. **智能路由**：简单问题使用便宜模型
2. **缓存机制**：避免重复调用
3. **请求限制**：控制使用频率

## 🆘 获取帮助

### 社区支持

- **GitHub Issues**: 报告问题和建议
- **微信群**: 加入用户交流群
- **文档**: 查看详细文档

### 联系方式

- 邮箱: <EMAIL>
- QQ群: 123456789
- 微信: xiaoai_support

---

🎉 **恭喜您完成小爱同学AI大模型升级！享受更智能的语音助手体验吧！**
