"""
MiGPT服务管理
"""

import asyncio
import subprocess
import docker
from typing import Dict, Any
from loguru import logger

class MiGPTService:
    """MiGPT服务管理器"""
    
    def __init__(self):
        self.container_name = "xiaoai-llm"
        self.docker_client = None
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            logger.warning(f"Docker客户端初始化失败: {e}")
    
    async def get_status(self) -> str:
        """获取MiGPT服务状态"""
        try:
            if not self.docker_client:
                return "unknown"
            
            containers = self.docker_client.containers.list(
                all=True, filters={"name": self.container_name}
            )
            
            if not containers:
                return "not_found"
            
            container = containers[0]
            status = container.status

            # 映射Docker状态到我们的状态
            if status == "running":
                return "running"
            elif status == "restarting":
                return "restarting"
            elif status == "exited":
                return "stopped"
            else:
                return status
            
        except Exception as e:
            logger.error(f"获取MiGPT状态失败: {e}")
            return "error"
    
    async def get_docker_status(self) -> str:
        """获取Docker状态"""
        try:
            if not self.docker_client:
                return "not_installed"
            
            # 尝试ping Docker
            self.docker_client.ping()
            return "running"
            
        except Exception as e:
            logger.error(f"获取Docker状态失败: {e}")
            return "error"
    
    async def restart(self) -> bool:
        """重启MiGPT容器"""
        try:
            if not self.docker_client:
                raise Exception("Docker客户端未初始化")
            
            containers = self.docker_client.containers.list(
                all=True,
                filters={"name": self.container_name}
            )
            
            if not containers:
                raise Exception(f"容器 {self.container_name} 不存在")
            
            container = containers[0]
            
            # 停止容器
            if container.status == "running":
                container.stop()
                logger.info(f"容器 {self.container_name} 已停止")
            
            # 启动容器
            container.start()
            logger.info(f"容器 {self.container_name} 已启动")
            
            return True
            
        except Exception as e:
            logger.error(f"重启MiGPT失败: {e}")
            return False
    
    async def get_logs(self, lines: int = 100) -> str:
        """获取MiGPT日志"""
        try:
            if not self.docker_client:
                return "Docker客户端未初始化"
            
            containers = self.docker_client.containers.list(
                all=True,
                filters={"name": self.container_name}
            )
            
            if not containers:
                return "容器不存在"
            
            container = containers[0]
            logs = container.logs(tail=lines).decode('utf-8', errors='ignore')
            return logs
            
        except Exception as e:
            logger.error(f"获取日志失败: {e}")
            return f"获取日志失败: {e}"
