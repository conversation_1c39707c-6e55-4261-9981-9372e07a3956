# Docker Compose 配置文件 - 增强版小爱同学AI升级
# 包含MiGPT主服务、Redis缓存、MongoDB数据库、监控服务

version: '3.8'

services:
  # ===========================================
  # MiGPT 主服务（增强版）
  # ===========================================
  xiaoai-llm:
    image: idootop/mi-gpt:latest
    container_name: xiaoai-llm-enhanced
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.enhanced
    volumes:
      - ./.migpt.enhanced.js:/app/.migpt.js:ro
      - ./logs:/app/logs
      - ./config:/app/config:ro
      - ./backups:/app/backups
    depends_on:
      - redis
      - mongodb
    networks:
      - xiaoai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # ===========================================
  # Redis 缓存服务
  # ===========================================
  redis:
    image: redis:7-alpine
    container_name: xiaoai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - xiaoai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

  # ===========================================
  # MongoDB 数据库服务
  # ===========================================
  mongodb:
    image: mongo:6
    container_name: xiaoai-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=${DATABASE_NAME:-xiaoai_llm}
    volumes:
      - mongodb_data:/data/db
      - ./config/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      - ./backups/mongodb:/backups
    networks:
      - xiaoai-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

  # ===========================================
  # Prometheus 监控服务
  # ===========================================
  prometheus:
    image: prom/prometheus:latest
    container_name: xiaoai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - xiaoai-network
    depends_on:
      - xiaoai-llm

  # ===========================================
  # Grafana 可视化面板
  # ===========================================
  grafana:
    image: grafana/grafana:latest
    container_name: xiaoai-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - xiaoai-network
    depends_on:
      - prometheus

  # ===========================================
  # Nginx 反向代理
  # ===========================================
  nginx:
    image: nginx:alpine
    container_name: xiaoai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - xiaoai-network
    depends_on:
      - xiaoai-llm
      - grafana

  # ===========================================
  # 日志收集服务
  # ===========================================
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: xiaoai-fluentd
    restart: unless-stopped
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./config/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/fluentd/log
    networks:
      - xiaoai-network

  # ===========================================
  # 定时备份服务
  # ===========================================
  backup:
    image: alpine:latest
    container_name: xiaoai-backup
    restart: unless-stopped
    environment:
      - BACKUP_INTERVAL=${BACKUP_INTERVAL:-24h}
      - BACKUP_RETENTION=${BACKUP_RETENTION:-7d}
    volumes:
      - ./backups:/backups
      - ./config/backup.sh:/backup.sh:ro
      - mongodb_data:/data/mongodb:ro
      - redis_data:/data/redis:ro
    command: sh -c "while true; do sh /backup.sh; sleep ${BACKUP_INTERVAL:-86400}; done"
    networks:
      - xiaoai-network
    depends_on:
      - mongodb
      - redis

# ===========================================
# 网络配置
# ===========================================
networks:
  xiaoai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===========================================
# 数据卷配置
# ===========================================
volumes:
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
