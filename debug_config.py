#!/usr/bin/env python3
"""
调试配置读取
"""

import asyncio
import sys
import sqlite3
import json
sys.path.append('backend')

from app.database import ConfigManager, AsyncSessionLocal, ConfigurationDB

async def debug_config():
    """调试配置读取"""
    print("🔍 调试配置读取...")

    # 直接查询数据库
    print("\n1. 直接查询SQLite数据库:")
    try:
        conn = sqlite3.connect("xiaoai.db")
        cursor = conn.cursor()
        cursor.execute("SELECT key, value FROM configurations")
        rows = cursor.fetchall()

        for key, value in rows:
            print(f"  {key}: {value[:100]}...")
        conn.close()
    except Exception as e:
        print(f"❌ 直接查询失败: {e}")

    # 使用异步会话查询
    print("\n2. 使用异步会话查询:")
    try:
        async with AsyncSessionLocal() as session:
            from sqlalchemy import select
            stmt = select(ConfigurationDB)
            result = await session.execute(stmt)
            configs = result.scalars().all()

            for config in configs:
                print(f"  {config.key}: {config.value[:100]}...")

    except Exception as e:
        print(f"❌ 异步查询失败: {e}")

    # 测试ConfigManager
    print("\n3. 测试ConfigManager:")
    keys_to_test = [
        "xiaomi_config",
        "xiaomi",
        "model_config",
        "models"
    ]

    for key in keys_to_test:
        try:
            config = await ConfigManager.get_config(key)
            print(f"  {key}: {config}")
        except Exception as e:
            print(f"  ❌ {key}: {e}")

if __name__ == "__main__":
    asyncio.run(debug_config())
