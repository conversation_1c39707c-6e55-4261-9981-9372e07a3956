@echo off
chcp 65001 >nul
echo ================================
echo 🤖 小爱同学AI管理器
echo ================================
echo.

echo [INFO] 检查环境依赖...

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python 未安装，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo [INFO] ✓ Python 环境正常

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo [INFO] ✓ Node.js 环境正常

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker 未安装，部分功能可能无法使用
    echo 下载地址: https://www.docker.com/products/docker-desktop
) else (
    echo [INFO] ✓ Docker 环境正常
)

echo.
echo ================================
echo 🚀 启动应用
echo ================================
echo.

echo 请选择启动模式：
echo 1) 开发模式（前后端分离）
echo 2) 生产模式（前端构建后集成）
echo 3) 仅启动后端API
echo 4) 仅启动前端界面
set /p mode="请输入选择 (1-4): "

if "%mode%"=="1" (
    goto dev_mode
) else if "%mode%"=="2" (
    goto prod_mode
) else if "%mode%"=="3" (
    goto backend_only
) else if "%mode%"=="4" (
    goto frontend_only
) else (
    echo [ERROR] 无效选择，默认使用开发模式
    goto dev_mode
)

:dev_mode
echo.
echo [INFO] 启动开发模式...
echo [INFO] 前端: http://localhost:3000
echo [INFO] 后端: http://localhost:8000
echo.

REM 启动后端
start "小爱AI管理器-后端" cmd /k "cd backend && python -m pip install -r requirements.txt && python main.py"

REM 等待后端启动
timeout /t 5 /nobreak >nul

REM 启动前端
start "小爱AI管理器-前端" cmd /k "cd xiaoai-app && npm install && npm start"

echo [INFO] 应用启动中，请稍候...
echo [INFO] 前端界面将在浏览器中自动打开
goto end

:prod_mode
echo.
echo [INFO] 启动生产模式...
echo [INFO] 正在构建前端...

cd xiaoai-app
call npm install
call npm run build
cd ..

echo [INFO] 启动后端服务...
cd backend
python -m pip install -r requirements.txt
python main.py
goto end

:backend_only
echo.
echo [INFO] 仅启动后端API...
echo [INFO] API地址: http://localhost:8000

cd backend
python -m pip install -r requirements.txt
python main.py
goto end

:frontend_only
echo.
echo [INFO] 仅启动前端界面...
echo [INFO] 前端地址: http://localhost:3000

cd xiaoai-app
call npm install
call npm start
goto end

:end
echo.
echo ================================
echo 📱 使用说明
echo ================================
echo.
echo 🎯 功能介绍：
echo   - 仪表盘：查看系统状态和使用统计
echo   - 对话界面：与AI增强的小爱同学对话
echo   - 验证助手：自动处理小米账号验证
echo   - 模型配置：配置AI模型API密钥
echo   - 小米配置：配置小米账号和设备信息
echo.
echo 🔧 配置步骤：
echo   1. 在"模型配置"中填入AI模型API密钥
echo   2. 在"小米配置"中填入小米账号信息
echo   3. 启动验证助手处理安全验证
echo   4. 开始与AI增强的小爱同学对话
echo.
echo 💡 提示：
echo   - 首次使用需要完成小米账号安全验证
echo   - 建议配置多个AI模型作为备用
echo   - 可以在仪表盘查看使用统计和成本
echo.
pause
