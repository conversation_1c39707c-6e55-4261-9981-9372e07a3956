import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Space, 
  Alert, 
  Select,
  List,
  Tag,
  message,
  Modal,
  Descriptions
} from 'antd';
import { 
  UserOutlined, 
  SaveOutlined, 
  TestOutlined, 
  ReloadOutlined,
  HomeOutlined,
  PhoneOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;

const XiaomiConfig = ({ socket }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [config, setConfig] = useState({});
  const [devices, setDevices] = useState([]);
  const [loginStatus, setLoginStatus] = useState('unknown');
  const [accountInfo, setAccountInfo] = useState(null);

  useEffect(() => {
    loadConfig();
    loadDevices();
  }, []);

  useEffect(() => {
    if (socket) {
      socket.on('xiaomi_login_status', (status) => {
        setLoginStatus(status.status);
        setAccountInfo(status.account_info);
      });

      socket.on('devices_updated', (deviceList) => {
        setDevices(deviceList);
      });

      return () => {
        socket.off('xiaomi_login_status');
        socket.off('devices_updated');
      };
    }
  }, [socket]);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/xiaomi-config');
      setConfig(response.data);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const loadDevices = async () => {
    try {
      const response = await axios.get('/api/xiaomi-devices');
      setDevices(response.data);
    } catch (error) {
      console.error('加载设备列表失败:', error);
    }
  };

  const saveConfig = async (values) => {
    try {
      setLoading(true);
      await axios.post('/api/xiaomi-config', values);
      setConfig(values);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setTestLoading(true);
      const response = await axios.post('/api/test-xiaomi-connection');
      
      if (response.data.success) {
        message.success('连接测试成功');
        setLoginStatus('connected');
        setAccountInfo(response.data.account_info);
        loadDevices();
      } else {
        message.error(`连接测试失败: ${response.data.message}`);
        setLoginStatus('failed');
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      message.error('测试连接失败');
      setLoginStatus('failed');
    } finally {
      setTestLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'green';
      case 'failed': case 'error': return 'red';
      case 'pending': return 'orange';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected': return '已连接';
      case 'failed': return '连接失败';
      case 'error': return '错误';
      case 'pending': return '连接中';
      default: return '未知';
    }
  };

  const showDeviceDetails = (device) => {
    Modal.info({
      title: '设备详情',
      width: 600,
      content: (
        <Descriptions column={1} bordered>
          <Descriptions.Item label="设备名称">{device.name}</Descriptions.Item>
          <Descriptions.Item label="设备ID">{device.did}</Descriptions.Item>
          <Descriptions.Item label="设备型号">{device.model}</Descriptions.Item>
          <Descriptions.Item label="设备类型">{device.type}</Descriptions.Item>
          <Descriptions.Item label="在线状态">
            <Tag color={device.online ? 'green' : 'red'}>
              {device.online ? '在线' : '离线'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="固件版本">{device.firmware_version}</Descriptions.Item>
          <Descriptions.Item label="IP地址">{device.ip}</Descriptions.Item>
          <Descriptions.Item label="MAC地址">{device.mac}</Descriptions.Item>
        </Descriptions>
      )
    });
  };

  return (
    <div className="xiaomi-config fade-in">
      <Card 
        title={
          <Space>
            <UserOutlined />
            小米账号配置
            <Tag color={getStatusColor(loginStatus)}>
              {getStatusText(loginStatus)}
            </Tag>
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadConfig}>
              重新加载
            </Button>
            <Button 
              icon={<TestOutlined />} 
              onClick={testConnection}
              loading={testLoading}
            >
              测试连接
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={() => form.submit()}
              loading={loading}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveConfig}
        >
          {/* 账号信息 */}
          <Card title="账号信息" style={{ marginBottom: 24 }}>
            <Alert
              message="小米账号配置说明"
              description="请填入您的小米账号信息。这些信息将用于连接小爱音箱设备。请确保账号信息正确且设备已添加到米家APP中。"
              type="info"
              style={{ marginBottom: 16 }}
            />

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
              <Form.Item
                name="userId"
                label="小米ID"
                rules={[
                  { required: true, message: '请输入小米ID' },
                  { pattern: /^\d+$/, message: '小米ID应为纯数字' }
                ]}
              >
                <Input 
                  placeholder="请输入小米ID（纯数字）" 
                  prefix={<UserOutlined />}
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password 
                  placeholder="请输入小米账号密码"
                  prefix={<SafetyOutlined />}
                />
              </Form.Item>
            </div>

            {/* 账号状态信息 */}
            {accountInfo && (
              <Card size="small" title="账号状态" style={{ marginTop: 16 }}>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="用户ID">{accountInfo.userId}</Descriptions.Item>
                  <Descriptions.Item label="昵称">{accountInfo.nickname}</Descriptions.Item>
                  <Descriptions.Item label="手机号">{accountInfo.phone}</Descriptions.Item>
                  <Descriptions.Item label="邮箱">{accountInfo.email}</Descriptions.Item>
                  <Descriptions.Item label="登录时间">{accountInfo.loginTime}</Descriptions.Item>
                  <Descriptions.Item label="登录状态">
                    <Tag color="green">已登录</Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )}
          </Card>

          {/* 设备配置 */}
          <Card title="设备配置" style={{ marginBottom: 24 }}>
            <Form.Item
              name="did"
              label="设备名称/DID"
              rules={[{ required: true, message: '请输入设备名称或DID' }]}
            >
              <Select
                placeholder="请选择或输入设备名称/DID"
                showSearch
                allowClear
                mode="combobox"
                prefix={<HomeOutlined />}
              >
                {devices.map(device => (
                  <Option key={device.did} value={device.did}>
                    <Space>
                      <span>{device.name}</span>
                      <Tag size="small" color={device.online ? 'green' : 'red'}>
                        {device.online ? '在线' : '离线'}
                      </Tag>
                      <span style={{ color: '#999', fontSize: '12px' }}>
                        {device.model}
                      </span>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Alert
              message="设备选择说明"
              description="请选择您要控制的小爱音箱设备。如果列表中没有您的设备，请确保设备已添加到米家APP中，并且账号登录成功。"
              type="info"
            />
          </Card>

          {/* 高级设置 */}
          <Card title="高级设置" style={{ marginBottom: 24 }}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
              <Form.Item
                name="tts"
                label="TTS引擎"
                initialValue="xiaoai"
              >
                <Select>
                  <Option value="xiaoai">小爱TTS</Option>
                  <Option value="system">系统TTS</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="timeout"
                label="超时时间(秒)"
                initialValue={5}
              >
                <Input type="number" min={1} max={30} />
              </Form.Item>

              <Form.Item
                name="exitKeepAliveAfter"
                label="退出保持时间(秒)"
                initialValue={30}
              >
                <Input type="number" min={10} max={300} />
              </Form.Item>

              <Form.Item
                name="checkInterval"
                label="检查间隔(毫秒)"
                initialValue={1000}
              >
                <Input type="number" min={500} max={5000} />
              </Form.Item>
            </div>
          </Card>
        </Form>

        {/* 设备列表 */}
        {devices.length > 0 && (
          <Card title="设备列表" style={{ marginTop: 24 }}>
            <List
              dataSource={devices}
              renderItem={device => (
                <List.Item
                  actions={[
                    <Button 
                      size="small" 
                      onClick={() => showDeviceDetails(device)}
                    >
                      查看详情
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<HomeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                    title={
                      <Space>
                        <span>{device.name}</span>
                        <Tag color={device.online ? 'green' : 'red'}>
                          {device.online ? '在线' : '离线'}
                        </Tag>
                        <Tag color="blue">{device.model}</Tag>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size="small">
                        <span>DID: {device.did}</span>
                        <span>IP: {device.ip}</span>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        )}
      </Card>
    </div>
  );
};

export default XiaomiConfig;
