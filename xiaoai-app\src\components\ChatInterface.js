import React, { useState, useEffect, useRef } from 'react';
import { Card, Input, Button, Avatar, Space, Tag, Spin, message, Select, Switch } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, ClearOutlined, DownloadOutlined } from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { TextArea } = Input;
const { Option } = Select;

const ChatInterface = ({ socket }) => {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('deepseek');
  const [autoScroll, setAutoScroll] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const messagesEndRef = useRef(null);

  const models = [
    { value: 'deepseek', label: 'DeepSeek', color: 'blue' },
    { value: 'wenxin', label: '文心一言', color: 'purple' },
    { value: 'qianwen', label: '通义千问', color: 'orange' },
    { value: 'gpt4', label: 'GPT-4', color: 'green' }
  ];

  useEffect(() => {
    loadChatHistory();
  }, []);

  useEffect(() => {
    if (socket) {
      socket.on('chat_response', (data) => {
        setMessages(prev => [...prev, {
          id: Date.now(),
          type: 'assistant',
          content: data.response,
          model: data.model,
          timestamp: new Date(),
          cost: data.cost,
          tokens: data.tokens
        }]);
        setLoading(false);
      });

      socket.on('connection_status', (status) => {
        setConnectionStatus(status);
      });

      return () => {
        socket.off('chat_response');
        socket.off('connection_status');
      };
    }
  }, [socket]);

  useEffect(() => {
    if (autoScroll) {
      scrollToBottom();
    }
  }, [messages, autoScroll]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadChatHistory = async () => {
    try {
      const response = await axios.get('/api/chat-history');
      setMessages(response.data);
    } catch (error) {
      console.error('加载聊天记录失败:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);
    setInputValue('');

    try {
      if (socket && socket.connected) {
        // 通过Socket发送消息（实时对话）
        socket.emit('send_message', {
          message: inputValue,
          model: selectedModel
        });
      } else {
        // 通过HTTP API发送消息（备用方案）
        const response = await axios.post('/api/chat', {
          message: inputValue,
          model: selectedModel
        });

        setMessages(prev => [...prev, {
          id: Date.now() + 1,
          type: 'assistant',
          content: response.data.response,
          model: response.data.model,
          timestamp: new Date(),
          cost: response.data.cost,
          tokens: response.data.tokens
        }]);
        setLoading(false);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请检查网络连接');
      setLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
    message.success('聊天记录已清空');
  };

  const exportChat = () => {
    const chatData = messages.map(msg => ({
      时间: moment(msg.timestamp).format('YYYY-MM-DD HH:mm:ss'),
      角色: msg.type === 'user' ? '用户' : '小爱同学',
      内容: msg.content,
      模型: msg.model || '',
      成本: msg.cost || '',
      Token数: msg.tokens || ''
    }));

    const dataStr = JSON.stringify(chatData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `小爱同学对话记录_${moment().format('YYYY-MM-DD_HH-mm-ss')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getModelColor = (model) => {
    const modelInfo = models.find(m => m.value === model);
    return modelInfo ? modelInfo.color : 'default';
  };

  const getModelLabel = (model) => {
    const modelInfo = models.find(m => m.value === model);
    return modelInfo ? modelInfo.label : model;
  };

  return (
    <div className="chat-interface fade-in">
      <Card 
        title={
          <Space>
            <RobotOutlined />
            小爱同学对话界面
            <Tag color={connectionStatus === 'connected' ? 'green' : 'red'}>
              {connectionStatus === 'connected' ? '已连接' : '未连接'}
            </Tag>
          </Space>
        }
        extra={
          <Space>
            <Select
              value={selectedModel}
              onChange={setSelectedModel}
              style={{ width: 120 }}
            >
              {models.map(model => (
                <Option key={model.value} value={model.value}>
                  <Tag color={model.color}>{model.label}</Tag>
                </Option>
              ))}
            </Select>
            <Switch
              checked={autoScroll}
              onChange={setAutoScroll}
              checkedChildren="自动滚动"
              unCheckedChildren="手动滚动"
            />
            <Button icon={<DownloadOutlined />} onClick={exportChat}>
              导出
            </Button>
            <Button icon={<ClearOutlined />} onClick={clearChat}>
              清空
            </Button>
          </Space>
        }
      >
        <div className="chat-container">
          <div className="chat-messages">
            {messages.map((message) => (
              <div key={message.id} className={`message ${message.type}`}>
                <div className="message-content">
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <Avatar 
                      icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />}
                      style={{ 
                        backgroundColor: message.type === 'user' ? '#1890ff' : '#52c41a',
                        marginRight: 8
                      }}
                    />
                    <Space>
                      <span style={{ fontWeight: 'bold' }}>
                        {message.type === 'user' ? '您' : '小爱同学'}
                      </span>
                      {message.model && (
                        <Tag color={getModelColor(message.model)} size="small">
                          {getModelLabel(message.model)}
                        </Tag>
                      )}
                      <span className="message-time">
                        {moment(message.timestamp).format('HH:mm:ss')}
                      </span>
                    </Space>
                  </div>
                  <div style={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}>
                    {message.content}
                  </div>
                  {message.cost && (
                    <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
                      成本: ${message.cost.toFixed(6)} | Token: {message.tokens}
                    </div>
                  )}
                </div>
              </div>
            ))}
            {loading && (
              <div className="message assistant">
                <div className="message-content">
                  <Space>
                    <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#52c41a' }} />
                    <Spin size="small" />
                    <span>小爱同学正在思考中...</span>
                  </Space>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="chat-input">
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入消息... (Shift+Enter换行，Enter发送)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={loading}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={sendMessage}
              loading={loading}
              disabled={!inputValue.trim()}
            >
              发送
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChatInterface;
