<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取小米验证链接</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: inline-block;
            text-decoration: none;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            word-break: break-all;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .verification-link {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .verification-link a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .verification-link a:hover {
            text-decoration: underline;
        }
        .status-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ 小米账号验证链接获取工具</h1>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button class="button" onclick="checkStatus()">检查系统状态</button>
            <button class="button success" onclick="getVerificationUrl()">获取验证链接</button>
            <button class="button warning" onclick="startMiGPT()">启动MiGPT容器</button>
        </div>

        <div id="result"></div>

        <div class="info" style="margin-top: 30px;">
            <h3>📋 使用说明:</h3>
            <ol>
                <li><strong>检查系统状态</strong> - 查看后端服务和容器状态</li>
                <li><strong>获取验证链接</strong> - 获取小米账号验证链接</li>
                <li><strong>点击验证链接</strong> - 在新窗口中完成验证</li>
                <li><strong>启动MiGPT容器</strong> - 验证完成后启动服务</li>
            </ol>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }

        async function checkStatus() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔍 正在检查系统状态...</div>';

            try {
                const status = await makeRequest(`${API_BASE}/api/status`);
                
                let statusHtml = '<div class="success"><h3>✅ 系统状态检查结果:</h3>';
                statusHtml += '<div class="status-info">';
                
                // MiGPT状态
                const migptClass = status.migpt_status === 'running' ? 'status-running' : 
                                 status.migpt_status === 'stopped' ? 'status-stopped' : 'status-warning';
                statusHtml += `<div class="status-card ${migptClass}">
                    <strong>📦 MiGPT容器</strong><br>
                    ${status.migpt_status}
                </div>`;
                
                // Docker状态
                const dockerClass = status.docker_status === 'running' ? 'status-running' : 'status-stopped';
                statusHtml += `<div class="status-card ${dockerClass}">
                    <strong>🐳 Docker服务</strong><br>
                    ${status.docker_status}
                </div>`;
                
                // 小米登录状态
                const loginClass = status.xiaomi_login === 'connected' ? 'status-running' : 'status-warning';
                statusHtml += `<div class="status-card ${loginClass}">
                    <strong>🔐 小米账号</strong><br>
                    ${status.xiaomi_login}
                </div>`;
                
                // AI模型状态
                const aiClass = status.ai_model === 'connected' ? 'status-running' : 'status-warning';
                statusHtml += `<div class="status-card ${aiClass}">
                    <strong>🤖 AI模型</strong><br>
                    ${status.ai_model}
                </div>`;
                
                statusHtml += '</div></div>';
                resultDiv.innerHTML = statusHtml;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 检查状态失败: ${error.message}</div>`;
            }
        }

        async function getVerificationUrl() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔗 正在获取验证链接...</div>';

            try {
                const data = await makeRequest(`${API_BASE}/api/verification-urls`);
                
                if (data.urls && data.urls.length > 0) {
                    let html = '<div class="success"><h3>✅ 验证链接获取成功!</h3>';
                    html += `<p>共找到 <strong>${data.urls.length}</strong> 个验证链接</p>`;
                    
                    // 显示最新的验证链接
                    const latestUrl = data.urls[0];
                    html += '<div class="verification-link">';
                    html += '<h4>🌐 点击下方链接完成验证:</h4>';
                    html += `<a href="${latestUrl}" target="_blank">打开小米账号验证页面</a>`;
                    html += '<p><small>💡 链接将在新窗口中打开，请按照页面提示完成验证</small></p>';
                    html += '</div>';
                    
                    html += '<div class="info">';
                    html += '<h4>📱 验证步骤:</h4>';
                    html += '<ol>';
                    html += '<li>点击上方链接打开验证页面</li>';
                    html += '<li>输入您的小米账号手机号</li>';
                    html += '<li>输入收到的短信验证码</li>';
                    html += '<li>完成安全验证</li>';
                    html += '<li>验证完成后返回此页面，点击"启动MiGPT容器"</li>';
                    html += '</ol>';
                    html += '</div>';
                    
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 没有找到可用的验证链接</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 获取验证链接失败: ${error.message}</div>`;
            }
        }

        async function startMiGPT() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🚀 正在启动MiGPT容器...</div>';

            try {
                const data = await makeRequest(`${API_BASE}/api/start-migpt`, {
                    method: 'POST'
                });
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 启动容器失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
