#!/usr/bin/env python3
"""
手动初始化数据库
"""

import asyncio
import sys
import os

# 添加backend目录到路径
sys.path.append('backend')

from app.database import init_db, ConfigManager
from datetime import datetime

async def manual_init():
    """手动初始化数据库"""
    print("🔧 手动初始化数据库...")
    
    try:
        await init_db()
        print("✅ 数据库初始化成功")
        
        # 测试配置功能
        print("🧪 测试配置功能...")
        
        # 设置测试配置
        await ConfigManager.set_config("test_key", {"test": "value", "timestamp": datetime.now().isoformat()})
        print("✅ 配置写入成功")

        # 读取配置
        config = await ConfigManager.get_config("test_key")
        print(f"✅ 配置读取成功: {config}")

        # 设置小米配置
        xiaomi_config = {
            "userId": "186339069",
            "password": "331256zL",
            "did": "老家小爱",
            "tts": "xiaoai"
        }
        await ConfigManager.set_config("xiaomi", xiaomi_config)
        print("✅ 小米配置写入成功")

        # 设置AI模型配置
        model_config = {
            "deepseek": {
                "api_key": "sk-test123456789",
                "enabled": True
            }
        }
        await ConfigManager.set_config("models", model_config)
        print("✅ AI模型配置写入成功")
        
        print("🎉 数据库初始化和配置完成！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(manual_init())
