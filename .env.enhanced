# 增强版环境配置文件
# 支持多模型AI大模型接入

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=production
LOG_LEVEL=info
DEBUG=false

# ===========================================  
# DeepSeek 配置（主力模型）
# ===========================================
OPENAI_API_KEY=sk-xxxxxx
OPENAI_MODEL=deepseek-chat
OPENAI_BASE_URL=https://cn.gptapi.asia/v1

# ===========================================
# 文心一言配置（备用模型）
# ===========================================
WENXIN_API_KEY=your_wenxin_api_key
WENXIN_SECRET_KEY=your_wenxin_secret_key
WENXIN_MODEL=ernie-bot-turbo
WENXIN_BASE_URL=https://aip.baidubce.com

# ===========================================
# 通义千问配置（阿里云）
# ===========================================
QIANWEN_API_KEY=your_qianwen_api_key
QIANWEN_MODEL=qwen-turbo
QIANWEN_BASE_URL=https://dashscope.aliyuncs.com

# ===========================================
# GPT-4 配置（高端模型）
# ===========================================
GPT4_API_KEY=your_gpt4_api_key
GPT4_MODEL=gpt-4
GPT4_BASE_URL=https://api.openai.com

# ===========================================
# 豆包配置（字节跳动）
# ===========================================
DOUBAO_API_KEY=your_doubao_api_key
DOUBAO_MODEL=doubao-pro-4k
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com

# ===========================================
# 智能路由配置
# ===========================================
# 默认使用的模型
DEFAULT_MODEL=deepseek
# 备用模型
FALLBACK_MODEL=wenxin
# 高端模型（复杂任务）
PREMIUM_MODEL=gpt4

# 路由策略
ENABLE_SMART_ROUTING=true
ROUTING_STRATEGY=cost_optimized  # cost_optimized, performance_first, balanced

# ===========================================
# 成本控制配置
# ===========================================
# 每日成本限制（美元）
DAILY_COST_LIMIT=5.0
# 每小时请求限制
HOURLY_REQUEST_LIMIT=100
# 成本预警阈值（百分比）
COST_WARNING_THRESHOLD=80

# ===========================================
# 缓存配置
# ===========================================
# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_TTL=3600

# 启用缓存
ENABLE_CACHE=true
CACHE_STRATEGY=lru  # lru, lfu, ttl

# ===========================================
# 数据库配置
# ===========================================
# MongoDB配置（存储对话历史）
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=xiaoai_llm
COLLECTION_NAME=conversations

# ===========================================
# 监控配置
# ===========================================
# 启用监控
ENABLE_MONITORING=true
MONITORING_PORT=9090

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true
RESPONSE_TIME_THRESHOLD=5000

# 错误监控
ENABLE_ERROR_TRACKING=true
MAX_RETRIES=3

# 日志配置
LOG_FILE=logs/xiaoai.log
LOG_ROTATION=daily
LOG_RETENTION=30d

# ===========================================
# 安全配置
# ===========================================
# 内容安全
ENABLE_CONTENT_FILTER=true
SENSITIVE_WORDS_FILE=config/sensitive_words.txt

# 频率限制
ENABLE_RATE_LIMIT=true
RATE_LIMIT_REQUESTS_PER_MINUTE=30
RATE_LIMIT_REQUESTS_PER_HOUR=500

# API安全
API_SECRET_KEY=your_secret_key_here
JWT_EXPIRES_IN=24h

# ===========================================
# 小米生态配置
# ===========================================
# 小米IoT API
XIAOMI_IOT_API_URL=https://api.io.mi.com
XIAOMI_IOT_API_KEY=your_xiaomi_iot_api_key

# 小米账号配置（从.migpt.js读取）
XIAOMI_USER_ID=
XIAOMI_PASSWORD=
XIAOMI_DEVICE_DID=

# ===========================================
# 通知配置
# ===========================================
# 邮件通知
ENABLE_EMAIL_NOTIFICATIONS=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# 微信通知
ENABLE_WECHAT_NOTIFICATIONS=false
WECHAT_WEBHOOK_URL=

# ===========================================
# 开发配置
# ===========================================
# 开发模式
DEVELOPMENT_MODE=false
MOCK_API_RESPONSES=false

# 测试配置
TEST_MODE=false
TEST_API_KEY=test_key

# ===========================================
# Docker配置
# ===========================================
# 容器配置
CONTAINER_NAME=xiaoai-llm-enhanced
CONTAINER_PORT=3000
CONTAINER_MEMORY=512m

# 网络配置
NETWORK_NAME=xiaoai-network
EXPOSE_PORT=3000

# ===========================================
# 备份配置
# ===========================================
# 数据备份
ENABLE_BACKUP=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION=7d
BACKUP_PATH=./backups

# 配置备份
BACKUP_CONFIG=true
CONFIG_BACKUP_PATH=./config_backups
