import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Alert, Space, Tag, Divider, notification, Switch } from 'antd';
import { 
  SafetyOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ReloadOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const VerificationHelper = ({ socket }) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState('idle');
  const [verificationUrls, setVerificationUrls] = useState([]);
  const [logs, setLogs] = useState([]);
  const [autoOpen, setAutoOpen] = useState(true);
  const [migptStatus, setMigptStatus] = useState('unknown');
  const logsEndRef = useRef(null);

  useEffect(() => {
    if (socket) {
      socket.on('verification_needed', (data) => {
        setVerificationStatus('pending');
        addLog('warning', '🚨 检测到需要安全验证！');
        
        if (data.url) {
          setVerificationUrls(prev => [...prev, {
            url: data.url,
            timestamp: new Date(),
            opened: false
          }]);
          
          if (autoOpen) {
            openVerificationUrl(data.url);
          }
        }
        
        notification.warning({
          message: '需要验证',
          description: '检测到小米账号需要安全验证，请完成验证',
          placement: 'topRight',
          duration: 0
        });
      });

      socket.on('verification_completed', () => {
        setVerificationStatus('success');
        addLog('success', '🎉 验证完成！');
        notification.success({
          message: '验证完成',
          description: '小米账号验证已完成',
          placement: 'topRight'
        });
      });

      socket.on('migpt_log', (data) => {
        addLog(data.level, data.message);
      });

      socket.on('migpt_status', (status) => {
        setMigptStatus(status);
      });

      return () => {
        socket.off('verification_needed');
        socket.off('verification_completed');
        socket.off('migpt_log');
        socket.off('migpt_status');
      };
    }
  }, [socket, autoOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [logs]);

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addLog = (level, message) => {
    const timestamp = moment().format('HH:mm:ss');
    setLogs(prev => [...prev, {
      id: Date.now(),
      level,
      message,
      timestamp
    }]);
  };

  const startMonitoring = async () => {
    try {
      await axios.post('/api/start-monitoring');
      setIsMonitoring(true);
      addLog('info', '🚀 开始监控MiGPT日志...');
    } catch (error) {
      console.error('启动监控失败:', error);
      addLog('error', '❌ 启动监控失败');
    }
  };

  const stopMonitoring = async () => {
    try {
      await axios.post('/api/stop-monitoring');
      setIsMonitoring(false);
      addLog('info', '⏹️ 停止监控');
    } catch (error) {
      console.error('停止监控失败:', error);
    }
  };

  const restartMiGPT = async () => {
    try {
      await axios.post('/api/restart-migpt');
      addLog('info', '🔄 正在重启MiGPT...');
      setVerificationUrls([]);
      setVerificationStatus('idle');
    } catch (error) {
      console.error('重启MiGPT失败:', error);
      addLog('error', '❌ 重启MiGPT失败');
    }
  };

  const openVerificationUrl = (url) => {
    window.open(url, '_blank');
    addLog('info', '🌐 验证页面已在新窗口中打开');
    
    // 更新URL状态为已打开
    setVerificationUrls(prev => 
      prev.map(item => 
        item.url === url ? { ...item, opened: true } : item
      )
    );
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = () => {
    switch (verificationStatus) {
      case 'success': return 'success';
      case 'pending': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const getStatusText = () => {
    switch (verificationStatus) {
      case 'success': return '验证已完成';
      case 'pending': return '等待验证';
      case 'error': return '验证失败';
      default: return '无需验证';
    }
  };

  const getStatusIcon = () => {
    switch (verificationStatus) {
      case 'success': return <CheckCircleOutlined />;
      case 'pending': return <ExclamationCircleOutlined />;
      case 'error': return <ExclamationCircleOutlined />;
      default: return <SafetyOutlined />;
    }
  };

  return (
    <div className="verification-helper fade-in">
      <Card 
        title={
          <Space>
            <SafetyOutlined />
            小米账号安全验证助手
            <Tag color={migptStatus === 'running' ? 'green' : 'red'}>
              MiGPT: {migptStatus === 'running' ? '运行中' : '未运行'}
            </Tag>
          </Space>
        }
      >
        {/* 验证状态 */}
        <Alert
          message={getStatusText()}
          description={
            verificationStatus === 'pending' 
              ? '检测到需要验证，请在浏览器中完成手机号验证'
              : verificationStatus === 'success'
              ? '验证已完成，小爱同学AI功能正常'
              : '系统正常运行，无需验证'
          }
          type={getStatusColor()}
          icon={getStatusIcon()}
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 控制面板 */}
        <Card size="small" title="控制面板" style={{ marginBottom: 16 }}>
          <Space wrap>
            <Button
              type={isMonitoring ? "default" : "primary"}
              icon={isMonitoring ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
            >
              {isMonitoring ? '停止监控' : '开始监控'}
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={restartMiGPT}
            >
              重启MiGPT
            </Button>
            
            <Button onClick={clearLogs}>
              清空日志
            </Button>
            
            <div>
              <Switch
                checked={autoOpen}
                onChange={setAutoOpen}
                checkedChildren="自动打开"
                unCheckedChildren="手动打开"
              />
              <span style={{ marginLeft: 8 }}>验证页面</span>
            </div>
          </Space>
        </Card>

        {/* 验证链接 */}
        {verificationUrls.length > 0 && (
          <Card size="small" title="验证链接" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {verificationUrls.map((item, index) => (
                <div key={index} className="verification-url">
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                    <Space>
                      <Tag color={item.opened ? 'green' : 'blue'}>
                        {item.opened ? '已打开' : '未打开'}
                      </Tag>
                      <span style={{ fontSize: '12px', color: '#999' }}>
                        {moment(item.timestamp).format('HH:mm:ss')}
                      </span>
                    </Space>
                    <Button
                      size="small"
                      icon={<LinkOutlined />}
                      onClick={() => openVerificationUrl(item.url)}
                    >
                      打开验证页面
                    </Button>
                  </div>
                  <div style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    wordBreak: 'break-all',
                    fontFamily: 'monospace'
                  }}>
                    {item.url}
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        )}

        <Divider>实时日志</Divider>

        {/* 日志显示 */}
        <div className="log-container">
          {logs.map((log) => (
            <div key={log.id} className={`log-entry ${log.level}`}>
              [{log.timestamp}] {log.message}
            </div>
          ))}
          {logs.length === 0 && (
            <div className="log-entry info">
              暂无日志，点击"开始监控"开始监控MiGPT日志
            </div>
          )}
          <div ref={logsEndRef} />
        </div>

        {/* 使用说明 */}
        <Card size="small" title="使用说明" style={{ marginTop: 16 }}>
          <ol style={{ paddingLeft: 20, margin: 0 }}>
            <li>点击"开始监控"开始监控MiGPT服务日志</li>
            <li>当检测到验证需求时，会自动在浏览器中打开验证页面</li>
            <li>在浏览器中完成手机号验证</li>
            <li>验证成功后需要等待约1小时才能生效</li>
            <li>如果验证失败，可以点击"重启MiGPT"重新获取验证链接</li>
          </ol>
        </Card>
      </Card>
    </div>
  );
};

export default VerificationHelper;
