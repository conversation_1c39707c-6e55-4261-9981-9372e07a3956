@echo off
chcp 65001 >nul
echo ================================
echo 小爱同学AI升级配置助手
echo ================================
echo.

echo 此助手将帮助您配置小爱同学AI升级项目
echo.

echo ================================
echo 第一步：配置API密钥
echo ================================
echo.
echo 您需要获取DeepSeek API密钥：
echo 1. 访问 https://platform.deepseek.com/
echo 2. 注册账号并获取API密钥
echo 3. 密钥格式类似：sk-xxxxxxxxxxxxxxxxxx
echo.

set /p api_key="请输入您的DeepSeek API密钥: "

if "%api_key%"=="" (
    echo [ERROR] API密钥不能为空
    pause
    exit /b 1
)

echo.
echo ================================
echo 第二步：配置小米账号
echo ================================
echo.
echo 您需要提供小米账号信息：
echo.

set /p xiaomi_id="请输入您的小米ID（数字）: "
set /p xiaomi_password="请输入您的小米账号密码: "
set /p device_name="请输入您的小爱音箱设备名称（如：小爱音箱Pro）: "

if "%xiaomi_id%"=="" (
    echo [ERROR] 小米ID不能为空
    pause
    exit /b 1
)

if "%xiaomi_password%"=="" (
    echo [ERROR] 小米账号密码不能为空
    pause
    exit /b 1
)

if "%device_name%"=="" (
    set device_name=小爱音箱Pro
    echo [INFO] 使用默认设备名称：小爱音箱Pro
)

echo.
echo ================================
echo 第三步：生成配置文件
echo ================================
echo.

REM 更新.env文件
echo [INFO] 更新API配置...
(
echo # 小爱同学AI大模型升级配置
echo # 自动生成于 %date% %time%
echo.
echo # DeepSeek 配置
echo OPENAI_API_KEY=%api_key%
echo OPENAI_MODEL=deepseek-chat
echo OPENAI_BASE_URL=https://cn.gptapi.asia/v1
echo.
echo # 其他配置
echo NODE_ENV=production
echo LOG_LEVEL=info
echo DEBUG=false
) > mi-gpt\.env

echo [INFO] ✓ API配置已保存到 mi-gpt\.env

REM 更新.migpt.js文件中的账号信息
echo [INFO] 更新小米账号配置...

powershell -Command "(Get-Content 'mi-gpt\.migpt.js') -replace 'userId: \"987654321\"', 'userId: \"%xiaomi_id%\"' | Set-Content 'mi-gpt\.migpt.js'"
powershell -Command "(Get-Content 'mi-gpt\.migpt.js') -replace 'password: \"123456\"', 'password: \"%xiaomi_password%\"' | Set-Content 'mi-gpt\.migpt.js'"
powershell -Command "(Get-Content 'mi-gpt\.migpt.js') -replace 'did: \"小爱音箱Pro\"', 'did: \"%device_name%\"' | Set-Content 'mi-gpt\.migpt.js'"

echo [INFO] ✓ 小米账号配置已更新

echo.
echo ================================
echo 🎉 配置完成！
echo ================================
echo.
echo 配置摘要：
echo 📱 小米ID: %xiaomi_id%
echo 🔑 API密钥: %api_key:~0,10%...
echo 🏠 设备名称: %device_name%
echo.
echo 下一步：
echo 1. 运行 "启动项目.bat" 启动服务
echo 2. 对小爱同学说话测试功能
echo.
echo ⚠️  重要提醒：
echo - 请确保您的小米账号信息正确
echo - 请确保API密钥有效且有余额
echo - 请确保网络连接正常
echo.
pause
