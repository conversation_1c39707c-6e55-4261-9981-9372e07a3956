#!/usr/bin/env python3
"""
智能小米验证助手
正确处理复杂的验证URL
"""

import re
import webbrowser
import subprocess
import json
from urllib.parse import unquote, quote
from datetime import datetime

class SmartVerificationHelper:
    def __init__(self):
        self.container_name = "xiaoai-llm"
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_verification_data(self, log_text):
        """从日志中提取验证数据"""
        verification_data = []
        
        # 提取 notificationUrl 中的验证链接
        notification_pattern = r'"notificationUrl":"([^"]+)"'
        matches = re.findall(notification_pattern, log_text)
        
        for match in matches:
            # 解码URL
            decoded_url = unquote(match)
            verification_data.append({
                'type': 'notification',
                'url': decoded_url,
                'source': 'notificationUrl'
            })
        
        # 提取直接的验证链接
        direct_patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)'
        ]
        
        for pattern in direct_patterns:
            matches = re.findall(pattern, log_text)
            for match in matches:
                verification_data.append({
                    'type': 'direct',
                    'url': match,
                    'source': 'direct_link'
                })
        
        return verification_data
    
    def clean_url(self, url):
        """清理和修复URL"""
        # 移除可能的换行符和空格
        url = re.sub(r'\s+', '', url)
        
        # 确保URL格式正确
        if not url.startswith('http'):
            return None
            
        return url
    
    def test_url_accessibility(self, url):
        """测试URL是否可访问"""
        try:
            import urllib.request
            req = urllib.request.Request(url, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            with urllib.request.urlopen(req, timeout=10) as response:
                return response.getcode() == 200
        except Exception as e:
            self.log(f"URL测试失败: {e}")
            return False
    
    def open_verification_url(self, url_data):
        """智能打开验证URL"""
        url = url_data['url']
        url_type = url_data['type']
        source = url_data['source']
        
        self.log(f"🔗 尝试打开验证链接 ({url_type} - {source})")
        
        # 清理URL
        clean_url = self.clean_url(url)
        if not clean_url:
            self.log("❌ URL格式无效")
            return False
        
        # 显示URL信息
        print(f"\n{'='*80}")
        print(f"🌐 验证链接类型: {url_type}")
        print(f"📋 来源: {source}")
        print(f"🔗 链接: {clean_url[:100]}...")
        print(f"{'='*80}\n")
        
        try:
            # 测试URL可访问性
            self.log("🧪 测试链接可访问性...")
            if not self.test_url_accessibility(clean_url):
                self.log("⚠️ 链接可能无法访问，但仍尝试打开")
            
            # 打开浏览器
            webbrowser.open(clean_url)
            self.log("✅ 验证页面已在浏览器中打开")
            self.log("📱 请在浏览器中完成验证")
            
            return True
            
        except Exception as e:
            self.log(f"❌ 打开浏览器失败: {e}")
            self.log("🔗 请手动复制以下链接到浏览器:")
            print(clean_url)
            return False
    
    def get_latest_verification_data(self):
        """获取最新的验证数据"""
        try:
            # 获取最新日志
            result = subprocess.run(
                ["docker", "logs", "--tail", "50", self.container_name],
                capture_output=True, text=True, check=True,
                encoding='utf-8', errors='ignore'
            )
            
            log_text = result.stdout
            verification_data = self.extract_verification_data(log_text)
            
            if not verification_data:
                self.log("❌ 未找到验证链接")
                return []
            
            # 去重并排序（最新的在前）
            unique_urls = {}
            for data in verification_data:
                url_key = data['url'][:100]  # 使用URL前100字符作为key
                if url_key not in unique_urls:
                    unique_urls[url_key] = data
            
            return list(unique_urls.values())
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 获取日志失败: {e}")
            return []
        except Exception as e:
            self.log(f"❌ 处理日志异常: {e}")
            return []
    
    def run_interactive(self):
        """交互式运行"""
        print("🚀 智能小米验证助手")
        print("="*60)
        
        while True:
            try:
                print("\n功能选项:")
                print("1. 获取并打开最新验证链接")
                print("2. 显示所有可用验证链接")
                print("3. 重启MiGPT容器")
                print("4. 退出")
                
                choice = input("\n请选择功能 (1-4): ").strip()
                
                if choice == "1":
                    self.log("🔍 获取最新验证数据...")
                    verification_data = self.get_latest_verification_data()
                    
                    if verification_data:
                        self.log(f"📋 找到 {len(verification_data)} 个验证链接")
                        # 优先使用第一个链接
                        success = self.open_verification_url(verification_data[0])
                        if not success and len(verification_data) > 1:
                            self.log("🔄 尝试备用链接...")
                            self.open_verification_url(verification_data[1])
                    else:
                        self.log("❌ 未找到验证链接，请先重启MiGPT容器")
                
                elif choice == "2":
                    self.log("🔍 获取所有验证链接...")
                    verification_data = self.get_latest_verification_data()
                    
                    if verification_data:
                        print(f"\n找到 {len(verification_data)} 个验证链接:")
                        for i, data in enumerate(verification_data, 1):
                            print(f"{i}. [{data['type']}] {data['url'][:80]}...")
                        
                        try:
                            idx = int(input("\n请选择要打开的链接 (输入序号): ")) - 1
                            if 0 <= idx < len(verification_data):
                                self.open_verification_url(verification_data[idx])
                            else:
                                print("❌ 无效的序号")
                        except ValueError:
                            print("❌ 请输入有效的数字")
                    else:
                        print("❌ 未找到验证链接")
                
                elif choice == "3":
                    self.log("🔄 重启MiGPT容器...")
                    try:
                        subprocess.run(["docker", "restart", self.container_name], 
                                     check=True, capture_output=True)
                        self.log("✅ 容器重启成功，请等待几秒钟后获取新的验证链接")
                    except subprocess.CalledProcessError as e:
                        self.log(f"❌ 重启失败: {e}")
                
                elif choice == "4":
                    print("👋 再见！")
                    break
                
                else:
                    print("❌ 无效选择，请输入 1-4")
                    
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 出错: {e}")

def main():
    """主函数"""
    helper = SmartVerificationHelper()
    helper.run_interactive()

if __name__ == "__main__":
    main()
