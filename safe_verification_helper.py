#!/usr/bin/env python3
"""
安全的小米账号验证助手
防止重复打开验证页面的BUG
"""

import re
import time
import webbrowser
import subprocess
from datetime import datetime
from urllib.parse import unquote

class SafeVerificationHelper:
    def __init__(self):
        self.container_name = "xiaoai-llm"
        self.opened_urls = set()  # 记录已打开的URL
        self.last_open_time = 0   # 最后打开时间
        self.min_interval = 60    # 最小间隔60秒
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_verification_urls(self, log_text):
        """从日志中提取验证URL"""
        patterns = [
            r'👉 (https://account\.xiaomi\.com/fe/service/verifyPhone[^\s]+)',
            r'👉 (https://account\.xiaomi\.com/identity/authStart[^\s]+)'
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, log_text)
            for match in matches:
                decoded_url = unquote(match)
                if decoded_url not in urls:
                    urls.append(decoded_url)
        
        return urls
    
    def should_open_url(self, url):
        """判断是否应该打开URL"""
        # 检查是否已经打开过
        if url in self.opened_urls:
            return False, "此验证链接已经打开过"
            
        # 检查时间间隔
        current_time = time.time()
        if current_time - self.last_open_time < self.min_interval:
            remaining = int(self.min_interval - (current_time - self.last_open_time))
            return False, f"请等待 {remaining} 秒后再打开新的验证链接"
            
        return True, "可以打开"
    
    def open_verification_url(self, url):
        """安全地打开验证URL"""
        should_open, reason = self.should_open_url(url)
        
        if not should_open:
            self.log(f"⚠️ 跳过打开: {reason}")
            return False
            
        try:
            self.log("🌐 正在打开验证页面...")
            webbrowser.open(url)
            
            # 记录已打开
            self.opened_urls.add(url)
            self.last_open_time = time.time()
            
            self.log("✅ 验证页面已在浏览器中打开")
            self.log("📱 请在浏览器中完成手机号验证")
            self.log("⏰ 验证成功后需要等待约1小时才能生效")
            
            print("\n" + "="*60)
            print("🔗 验证链接已自动打开")
            print("📋 如需手动打开，请复制以下链接:")
            print(url)
            print("="*60 + "\n")
            
            return True
            
        except Exception as e:
            self.log(f"❌ 打开浏览器失败: {e}")
            return False
    
    def get_latest_logs(self, lines=20):
        """获取最新的Docker日志"""
        try:
            result = subprocess.run(
                ["docker", "logs", "--tail", str(lines), self.container_name],
                capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore'
            )
            return result.stdout or ""
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 获取日志失败: {e}")
            return ""
        except Exception as e:
            self.log(f"❌ 获取日志异常: {e}")
            return ""
    
    def check_verification_needed(self):
        """检查是否需要验证"""
        logs = self.get_latest_logs(50)
        
        if "触发小米账号异地登录安全验证机制" in logs:
            self.log("🚨 检测到需要安全验证")
            
            # 提取验证URL
            urls = self.extract_verification_urls(logs)
            
            if urls:
                self.log(f"🔗 发现 {len(urls)} 个验证链接")
                
                # 只打开第一个未打开的链接
                for url in urls:
                    if self.open_verification_url(url):
                        break  # 成功打开一个就停止
                        
                return True
            else:
                self.log("⚠️ 检测到验证需求但未找到验证链接")
                
        return False
    
    def check_login_status(self):
        """检查登录状态"""
        logs = self.get_latest_logs(20)

        if not logs:
            self.log("⚠️ 无法获取日志")
            return "unknown"

        if "登录成功" in logs or "连接成功" in logs:
            self.log("🎉 小米账号登录成功！")
            return "success"
        elif "小米账号登录失败" in logs:
            self.log("❌ 小米账号登录失败")
            return "failed"
        else:
            return "unknown"
    
    def run_once(self):
        """运行一次检查"""
        print("🔍 检查验证状态...")
        
        # 检查Docker容器
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            
            if self.container_name not in result.stdout:
                self.log("❌ MiGPT容器未运行")
                return False
                
        except subprocess.CalledProcessError:
            self.log("❌ 无法检查Docker状态")
            return False
        
        # 检查登录状态
        status = self.check_login_status()
        if status == "success":
            self.log("✅ 账号已登录，无需验证")
            return True
        elif status == "failed":
            # 检查是否需要验证
            if self.check_verification_needed():
                self.log("🛡️ 已处理验证需求")
            else:
                self.log("⚠️ 登录失败但未检测到验证需求")
        else:
            self.log("📊 登录状态未知，继续监控...")
            
        return True
    
    def run_interactive(self):
        """交互式运行"""
        print("🚀 安全验证助手")
        print("="*50)
        print("功能:")
        print("1. 检查验证状态")
        print("2. 手动获取验证链接")
        print("3. 查看登录状态")
        print("4. 退出")
        print("="*50)
        
        while True:
            try:
                choice = input("\n请选择功能 (1-4): ").strip()
                
                if choice == "1":
                    self.run_once()
                elif choice == "2":
                    logs = self.get_latest_logs(100)
                    urls = self.extract_verification_urls(logs)
                    if urls:
                        print(f"\n找到 {len(urls)} 个验证链接:")
                        for i, url in enumerate(urls, 1):
                            print(f"{i}. {url}")
                        
                        try:
                            idx = int(input("请选择要打开的链接 (输入序号): ")) - 1
                            if 0 <= idx < len(urls):
                                # 临时允许打开
                                self.last_open_time = 0
                                self.open_verification_url(urls[idx])
                            else:
                                print("❌ 无效的序号")
                        except ValueError:
                            print("❌ 请输入有效的数字")
                    else:
                        print("❌ 未找到验证链接")
                        
                elif choice == "3":
                    status = self.check_login_status()
                    print(f"登录状态: {status}")
                    
                elif choice == "4":
                    print("👋 再见！")
                    break
                    
                else:
                    print("❌ 无效选择，请输入 1-4")
                    
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 出错: {e}")

def main():
    """主函数"""
    helper = SafeVerificationHelper()
    
    # 先运行一次检查
    if helper.run_once():
        # 然后进入交互模式
        helper.run_interactive()

if __name__ == "__main__":
    main()
