#!/usr/bin/env python3
"""
配置测试工具
"""

import requests
import json
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_xiaomi_config():
    """测试小米配置"""
    base_url = "http://localhost:8000"
    
    # 配置小米账号
    xiaomi_config = {
        "userId": "186339069",
        "password": "331256zL",
        "did": "老家小爱",
        "tts": "xiaoai",
        "timeout": 5,
        "exitKeepAliveAfter": 30,
        "checkInterval": 1000
    }
    
    try:
        log("🔧 配置小米账号...")
        response = requests.post(
            f"{base_url}/api/xiaomi-config",
            json=xiaomi_config,
            timeout=10
        )
        log(f"配置响应: {response.status_code}")
        if response.status_code == 200:
            log("✅ 小米账号配置成功")
        else:
            log(f"❌ 配置失败: {response.text}")
            
    except Exception as e:
        log(f"❌ 配置失败: {e}")
    
    # 检查状态变化
    try:
        log("🔍 检查状态变化...")
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            log(f"✅ 新状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            log(f"❌ 获取状态失败: {response.status_code}")
    except Exception as e:
        log(f"❌ 获取状态失败: {e}")

def test_ai_model_config():
    """测试AI模型配置"""
    base_url = "http://localhost:8000"
    
    # 配置AI模型
    model_config = {
        "deepseek": {
            "api_key": "sk-test123456789",
            "enabled": True,
            "base_url": "https://api.deepseek.com",
            "model": "deepseek-chat"
        },
        "wenxin": {
            "api_key": "test-wenxin-key",
            "enabled": False,
            "base_url": "https://aip.baidubce.com",
            "model": "ernie-bot"
        }
    }
    
    try:
        log("🤖 配置AI模型...")
        response = requests.post(
            f"{base_url}/api/model-config",
            json=model_config,
            timeout=10
        )
        log(f"配置响应: {response.status_code}")
        if response.status_code == 200:
            log("✅ AI模型配置成功")
        else:
            log(f"❌ 配置失败: {response.text}")
            
    except Exception as e:
        log(f"❌ 配置失败: {e}")
    
    # 检查状态变化
    try:
        log("🔍 检查状态变化...")
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            log(f"✅ 新状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            log(f"❌ 获取状态失败: {response.status_code}")
    except Exception as e:
        log(f"❌ 获取状态失败: {e}")

def main():
    print("🧪 配置测试工具")
    print("="*50)
    
    # 测试小米配置
    test_xiaomi_config()
    print("\n" + "="*50)
    
    # 测试AI模型配置
    test_ai_model_config()
    print("\n" + "="*50)
    
    print("✅ 配置测试完成")

if __name__ == "__main__":
    main()
