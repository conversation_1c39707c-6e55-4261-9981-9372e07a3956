#!/usr/bin/env python3
"""
测试前端状态显示
"""

import requests
import time
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_status_api():
    """测试状态API"""
    try:
        log("🔍 测试状态API...")
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        
        if response.status_code == 200:
            status = response.json()
            log("✅ 状态API响应正常")
            log(f"📊 当前状态:")
            log(f"  📱 MiGPT: {status.get('migpt_status', 'unknown')}")
            log(f"  🐳 Docker: {status.get('docker_status', 'unknown')}")
            log(f"  🔐 小米登录: {status.get('xiaomi_login', 'unknown')}")
            log(f"  🤖 AI模型: {status.get('ai_model', 'unknown')}")
            
            # 状态映射测试
            log("\n🎨 前端状态映射:")
            
            # MiGPT状态映射
            migpt_status = status.get('migpt_status', 'unknown')
            if migpt_status == 'running':
                log("  📱 MiGPT: 🟢 运行正常")
            elif migpt_status == 'restarting':
                log("  📱 MiGPT: 🟡 重启中")
            elif migpt_status == 'stopped':
                log("  📱 MiGPT: 🔴 已停止")
            else:
                log("  📱 MiGPT: 🟡 检查中")
            
            # 小米账号状态映射
            xiaomi_status = status.get('xiaomi_login', 'unknown')
            if xiaomi_status == 'connected':
                log("  🔐 小米账号: 🟢 已连接")
            elif xiaomi_status == 'need_verification':
                log("  🔐 小米账号: 🟡 需要验证")
            elif xiaomi_status == 'login_failed':
                log("  🔐 小米账号: 🔴 登录失败")
            elif xiaomi_status == 'configured':
                log("  🔐 小米账号: 🟡 已配置")
            else:
                log("  🔐 小米账号: 🔴 需要配置")
            
            # AI模型状态映射
            ai_status = status.get('ai_model', 'unknown')
            if ai_status == 'connected':
                log("  🤖 AI模型: 🟢 已连接")
            elif ai_status == 'configured':
                log("  🤖 AI模型: 🟡 已配置")
            else:
                log("  🤖 AI模型: 🔴 需要配置")
            
            return status
        else:
            log(f"❌ 状态API响应错误: {response.status_code}")
            return None
            
    except Exception as e:
        log(f"❌ 状态API测试失败: {e}")
        return None

def test_verification_urls():
    """测试验证URL"""
    try:
        log("\n🔗 测试验证URL...")
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            count = data.get('count', 0)
            
            log(f"✅ 检测到 {count} 个验证链接")
            if urls:
                log("🔗 最新验证链接:")
                latest_url = urls[0]
                log(f"  {latest_url[:100]}...")
            
            return data
        else:
            log(f"❌ 验证URL响应错误: {response.status_code}")
            return None
            
    except Exception as e:
        log(f"❌ 验证URL测试失败: {e}")
        return None

def main():
    print("🧪 前端状态显示测试")
    print("="*60)
    
    # 测试状态API
    status = test_status_api()
    
    # 测试验证URL
    verification = test_verification_urls()
    
    print("\n" + "="*60)
    log("🎉 测试完成！")
    
    if status:
        print("\n💡 前端界面应该显示:")
        print("  📱 MiGPT状态: 根据实际状态显示对应颜色")
        print("  🔐 小米账号: 根据实际状态显示对应颜色")
        print("  🤖 AI模型: 根据实际状态显示对应颜色")
        print("\n🌐 请检查浏览器中的前端界面是否正确显示状态")
    
    if verification and verification.get('count', 0) > 0:
        print(f"\n🛡️ 验证提醒: 检测到 {verification['count']} 个验证链接")
        print("  可以使用智能验证助手获取最新链接")

if __name__ == "__main__":
    main()
