# 🤖 小爱同学AI管理器

一个完整的Web应用，用于管理和升级小爱同学智能助手，支持AI大模型接入、自动验证、配置管理等功能。

## ✨ 核心特性

### 🎯 智能对话界面
- **实时对话**：与AI增强的小爱同学进行实时对话
- **多模型支持**：支持DeepSeek、文心一言、通义千问、GPT-4等多个AI模型
- **智能路由**：根据问题复杂度自动选择最合适的模型
- **对话历史**：完整的对话记录和导出功能

### 🛡️ 自动验证助手
- **智能监控**：实时监控MiGPT日志，检测验证需求
- **自动跳转**：检测到验证需求时自动在浏览器中打开验证页面
- **状态追踪**：实时显示验证状态和进度
- **日志查看**：完整的验证过程日志记录

### ⚙️ 配置管理系统
- **AI模型配置**：支持多个AI模型的API密钥配置和参数调整
- **小米账号配置**：小米账号信息和设备配置管理
- **智能路由配置**：自定义模型选择策略
- **成本控制**：设置每日成本限制和预警阈值

### 📊 监控仪表盘
- **系统状态**：实时显示MiGPT、Docker、小米账号等服务状态
- **使用统计**：对话数量、API调用、成本统计等
- **性能监控**：响应时间、成功率等性能指标
- **可视化图表**：使用趋势和模型分布图表

## 🏗️ 技术架构

### 前端技术栈
- **React 18**: 现代化的用户界面框架
- **Ant Design**: 企业级UI组件库
- **Socket.IO**: 实时通信
- **Recharts**: 数据可视化图表
- **Axios**: HTTP客户端

### 后端技术栈
- **FastAPI**: 高性能Python Web框架
- **Socket.IO**: WebSocket实时通信
- **SQLAlchemy**: 数据库ORM
- **Pydantic**: 数据验证和序列化
- **Docker**: 容器化部署

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   MiGPT服务     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 对话界面    │ │◄──►│ │ WebSocket   │ │◄──►│ │ Docker容器  │ │
│ │ 配置管理    │ │    │ │ REST API    │ │    │ │ 日志监控    │ │
│ │ 监控面板    │ │    │ │ 验证助手    │ │    │ │ 小米连接    │ │
│ │ 验证助手    │ │    │ │ 模型管理    │ │    │ │ AI模型调用  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- **Python 3.8+**
- **Node.js 16+**
- **Docker Desktop**（可选，用于MiGPT部署）

### 一键启动
```bash
# Windows用户
启动小爱AI管理器.bat

# 选择启动模式：
# 1) 开发模式（前后端分离）
# 2) 生产模式（前端构建后集成）
# 3) 仅启动后端API
# 4) 仅启动前端界面
```

### 手动启动

#### 1. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python main.py
```

#### 2. 启动前端界面
```bash
cd xiaoai-app
npm install
npm start
```

### 访问应用
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📋 使用指南

### 第一步：配置AI模型
1. 打开"模型配置"页面
2. 填入您的AI模型API密钥：
   - DeepSeek API Key
   - 文心一言 API Key（可选）
   - 通义千问 API Key（可选）
   - GPT-4 API Key（可选）
3. 点击"测试"验证配置
4. 保存配置

### 第二步：配置小米账号
1. 打开"小米配置"页面
2. 填入小米账号信息：
   - 小米ID（纯数字）
   - 小米账号密码
   - 小爱音箱设备名称
3. 点击"测试连接"验证配置
4. 保存配置

### 第三步：处理安全验证
1. 打开"验证助手"页面
2. 点击"开始监控"
3. 当检测到验证需求时，会自动打开浏览器
4. 在浏览器中完成手机号验证
5. 等待约1小时验证生效

### 第四步：开始对话
1. 打开"对话界面"页面
2. 选择要使用的AI模型
3. 输入消息开始与AI增强的小爱同学对话
4. 查看对话历史和成本统计

## 🎯 功能详解

### 对话界面
- **实时对话**：支持实时消息发送和接收
- **模型选择**：可以选择不同的AI模型进行对话
- **消息历史**：完整的对话记录保存
- **成本显示**：每条消息的Token数量和成本
- **导出功能**：支持对话记录导出

### 验证助手
- **自动监控**：实时监控MiGPT容器日志
- **智能检测**：自动检测小米账号验证需求
- **浏览器跳转**：自动在浏览器中打开验证页面
- **状态追踪**：实时显示验证状态和进度
- **日志记录**：完整的验证过程日志

### 配置管理
- **模型配置**：支持多个AI模型的配置管理
- **参数调整**：可调整温度、最大Token等参数
- **路由策略**：配置智能路由和备用模型
- **成本控制**：设置每日成本限制和预警

### 监控面板
- **系统状态**：实时显示各服务运行状态
- **使用统计**：对话数量、API调用次数等
- **成本分析**：每日成本、模型使用分布
- **性能指标**：响应时间、成功率等

## 🔧 高级配置

### 环境变量配置
创建 `.env` 文件：
```bash
# AI模型配置
DEEPSEEK_API_KEY=your_deepseek_api_key
WENXIN_API_KEY=your_wenxin_api_key
WENXIN_SECRET_KEY=your_wenxin_secret_key
QIANWEN_API_KEY=your_qianwen_api_key
OPENAI_API_KEY=your_openai_api_key

# 小米配置
XIAOMI_USER_ID=your_xiaomi_user_id
XIAOMI_PASSWORD=your_xiaomi_password
XIAOMI_DEVICE_DID=your_device_name

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./xiaoai.db
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_secret_key_here
```

### Docker部署
```bash
# 构建镜像
docker build -t xiaoai-manager .

# 运行容器
docker run -d \
  --name xiaoai-manager \
  -p 8000:8000 \
  -v $(pwd)/.env:/app/.env \
  xiaoai-manager
```

## 🛠️ 开发指南

### 项目结构
```
xiaoai-ai-manager/
├── xiaoai-app/                 # 前端React应用
│   ├── src/
│   │   ├── components/         # React组件
│   │   ├── App.js             # 主应用组件
│   │   └── App.css            # 样式文件
│   └── package.json           # 前端依赖
├── backend/                   # 后端FastAPI应用
│   ├── app/
│   │   ├── models.py          # 数据模型
│   │   ├── config.py          # 配置管理
│   │   └── services/          # 业务服务
│   ├── main.py               # 主应用入口
│   └── requirements.txt      # 后端依赖
├── 启动小爱AI管理器.bat        # Windows启动脚本
└── README.md                 # 项目说明
```

### 添加新功能
1. **前端组件**：在 `xiaoai-app/src/components/` 中添加新组件
2. **后端API**：在 `backend/main.py` 中添加新的API端点
3. **数据模型**：在 `backend/app/models.py` 中定义数据结构
4. **业务逻辑**：在 `backend/app/services/` 中实现业务逻辑

## 🐛 故障排除

### 常见问题

#### 1. 前端无法连接后端
- 检查后端服务是否启动（http://localhost:8000）
- 检查防火墙设置
- 确认端口没有被占用

#### 2. AI模型调用失败
- 检查API密钥是否正确
- 确认API余额是否充足
- 检查网络连接

#### 3. 小米账号验证失败
- 确认账号密码正确
- 完成浏览器中的安全验证
- 等待验证生效（约1小时）

#### 4. MiGPT容器无法启动
- 检查Docker Desktop是否运行
- 确认容器配置文件正确
- 查看容器日志排查问题

### 日志查看
```bash
# 后端日志
tail -f backend/logs/xiaoai.log

# MiGPT容器日志
docker logs -f xiaoai-llm

# 前端开发日志
# 在前端启动终端中查看
```

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [MiGPT](https://github.com/idootop/mi-gpt) - 小爱同学AI升级的核心项目
- [React](https://reactjs.org/) - 前端框架
- [FastAPI](https://fastapi.tiangolo.com/) - 后端框架
- [Ant Design](https://ant.design/) - UI组件库

---

🎉 **享受您的AI增强版小爱同学吧！**
