/* 全局样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-text {
  margin-top: 20px;
  font-size: 18px;
  font-weight: 500;
}

/* Logo样式 */
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
}

.logo-text {
  margin-left: 12px;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

/* 头部样式 */
.site-layout-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-title {
  color: white;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 内容区域样式 */
.site-layout-content {
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 112px);
}

/* 卡片样式 */
.dashboard-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.status-card {
  text-align: center;
  padding: 24px;
}

.status-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-description {
  color: #666;
  font-size: 14px;
}

/* 聊天界面样式 */
.chat-container {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 16px;
}

.message {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.message.user .message-content {
  background: #1890ff;
  color: white;
}

.message.assistant .message-content {
  background: white;
  border: 1px solid #d9d9d9;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.chat-input {
  display: flex;
  gap: 8px;
}

/* 配置表单样式 */
.config-form {
  max-width: 600px;
}

.config-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.config-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1890ff;
}

/* 验证助手样式 */
.verification-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.verification-status {
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.verification-status.pending {
  background: #fff7e6;
  border: 1px solid #ffd591;
  color: #fa8c16;
}

.verification-status.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.verification-status.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.verification-url {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  font-family: monospace;
  word-break: break-all;
  border-left: 4px solid #1890ff;
}

/* 日志显示样式 */
.log-container {
  height: 400px;
  overflow-y: auto;
  background: #001529;
  color: #fff;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.log-entry {
  margin-bottom: 4px;
  white-space: pre-wrap;
}

.log-entry.info {
  color: #1890ff;
}

.log-entry.success {
  color: #52c41a;
}

.log-entry.warning {
  color: #faad14;
}

.log-entry.error {
  color: #ff4d4f;
}

/* 统计图表样式 */
.chart-container {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-layout-content {
    margin: 16px;
    padding: 16px;
  }
  
  .header-title {
    font-size: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .config-form {
    max-width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
