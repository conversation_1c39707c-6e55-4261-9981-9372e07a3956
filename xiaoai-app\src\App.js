import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Menu, Badge, notification, Spin } from 'antd';
import {
  MessageOutlined,
  SettingOutlined,
  DashboardOutlined,
  SafetyOutlined,
  ApiOutlined,
  UserOutlined,
  HomeOutlined
} from '@ant-design/icons';
import io from 'socket.io-client';

// 导入页面组件
import Dashboard from './components/Dashboard';
import ChatInterface from './components/ChatInterface';
import Configuration from './components/Configuration';
import VerificationHelper from './components/VerificationHelper';
import ModelConfig from './components/ModelConfig';
import XiaomiConfig from './components/XiaomiConfig';

import './App.css';

const { Header, Sider, Content } = Layout;

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [socket, setSocket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [verificationCount, setVerificationCount] = useState(0);

  useEffect(() => {
    // 初始化Socket连接
    const newSocket = io('http://localhost:8000');
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setConnectionStatus('connected');
      notification.success({
        message: '连接成功',
        description: '已连接到小爱同学AI服务',
        placement: 'topRight'
      });
    });

    newSocket.on('disconnect', () => {
      setConnectionStatus('disconnected');
      notification.warning({
        message: '连接断开',
        description: '与小爱同学AI服务的连接已断开',
        placement: 'topRight'
      });
    });

    newSocket.on('verification_needed', (data) => {
      setVerificationCount(prev => prev + 1);
      notification.warning({
        message: '需要验证',
        description: '检测到小米账号需要安全验证',
        placement: 'topRight',
        duration: 0
      });
    });

    newSocket.on('verification_completed', () => {
      setVerificationCount(0);
      notification.success({
        message: '验证完成',
        description: '小米账号验证已完成',
        placement: 'topRight'
      });
    });

    // 模拟加载完成
    setTimeout(() => setLoading(false), 1000);

    return () => newSocket.close();
  }, []);

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
      path: '/dashboard'
    },
    {
      key: 'chat',
      icon: <MessageOutlined />,
      label: '对话界面',
      path: '/chat'
    },
    {
      key: 'verification',
      icon: <SafetyOutlined />,
      label: (
        <Badge count={verificationCount} size="small">
          验证助手
        </Badge>
      ),
      path: '/verification'
    },
    {
      key: 'config',
      icon: <SettingOutlined />,
      label: '配置管理',
      children: [
        {
          key: 'model-config',
          icon: <ApiOutlined />,
          label: 'AI模型配置',
          path: '/config/models'
        },
        {
          key: 'xiaomi-config',
          icon: <UserOutlined />,
          label: '小米账号配置',
          path: '/config/xiaomi'
        }
      ]
    }
  ];

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#52c41a';
      case 'connecting': return '#faad14';
      default: return '#ff4d4f';
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected': return '已连接';
      case 'connecting': return '连接中';
      default: return '未连接';
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <div className="loading-text">🤖 小爱同学AI管理器启动中...</div>
      </div>
    );
  }

  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider 
          collapsible 
          collapsed={collapsed} 
          onCollapse={setCollapsed}
          theme="dark"
          width={250}
        >
          <div className="logo">
            <HomeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            {!collapsed && <span className="logo-text">小爱AI管理器</span>}
          </div>
          
          <Menu
            theme="dark"
            defaultSelectedKeys={['dashboard']}
            mode="inline"
            items={menuItems.map(item => ({
              ...item,
              onClick: item.path ? () => window.location.hash = item.path : undefined
            }))}
          />
        </Sider>

        <Layout className="site-layout">
          <Header className="site-layout-header">
            <div className="header-content">
              <h1 className="header-title">小爱同学AI大模型管理器</h1>
              <div className="header-status">
                <Badge 
                  color={getConnectionColor()} 
                  text={getConnectionText()}
                  style={{ color: 'white' }}
                />
              </div>
            </div>
          </Header>

          <Content className="site-layout-content">
            <Routes>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={<Dashboard socket={socket} />} />
              <Route path="/chat" element={<ChatInterface socket={socket} />} />
              <Route path="/verification" element={<VerificationHelper socket={socket} />} />
              <Route path="/config/models" element={<ModelConfig socket={socket} />} />
              <Route path="/config/xiaomi" element={<XiaomiConfig socket={socket} />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;
