#!/usr/bin/env python3
"""
小米账号和设备诊断工具
深度分析MiGPT容器和小米账号问题
"""

import subprocess
import requests
import json
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_container_config():
    """检查容器配置"""
    log("🔍 检查MiGPT容器配置...")
    
    try:
        # 检查容器挂载的配置文件
        result = subprocess.run([
            "docker", "exec", "xiaoai-llm", "cat", "/app/.migpt.js"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            log("✅ 容器内配置文件:")
            config_lines = result.stdout.split('\n')
            for i, line in enumerate(config_lines):
                if 'userId' in line or 'password' in line or 'did' in line:
                    log(f"  第{i+1}行: {line.strip()}")
        else:
            log("❌ 无法读取容器内配置文件")
            
    except Exception as e:
        log(f"❌ 检查容器配置失败: {e}")

def check_xiaomi_devices():
    """检查小米设备信息"""
    log("🔍 检查小米设备信息...")
    
    # 这里我们需要分析可能的设备ID问题
    log("💡 设备ID分析:")
    log("  当前配置: did: '老家小爱'")
    log("  可能问题:")
    log("    1. 设备名称包含中文字符")
    log("    2. 设备名称与米家APP中的名称不匹配")
    log("    3. 需要使用设备的实际DID而不是自定义名称")

def check_account_verification():
    """检查账号验证状态"""
    log("🔍 检查小米账号验证状态...")
    
    try:
        # 从验证监控获取最新验证链接
        response = requests.get("http://localhost:8000/api/verification-urls", timeout=5)
        if response.status_code == 200:
            data = response.json()
            urls = data.get('urls', [])
            count = data.get('count', 0)
            
            log(f"📊 检测到 {count} 个验证链接")
            if urls:
                latest_url = urls[0]
                log("🔗 最新验证链接:")
                log(f"  {latest_url[:100]}...")
                
                # 分析验证链接的状态
                if "context=" in latest_url:
                    log("✅ 验证链接格式正常")
                else:
                    log("⚠️ 验证链接格式异常")
            else:
                log("❌ 没有检测到验证链接")
        else:
            log("❌ 无法获取验证链接")
            
    except Exception as e:
        log(f"❌ 检查验证状态失败: {e}")

def analyze_login_failure():
    """分析登录失败原因"""
    log("🔍 分析登录失败原因...")
    
    failure_reasons = [
        "1. 🛡️ 小米账号异地登录安全验证未完成",
        "2. 📱 设备ID配置错误 (did: '老家小爱')",
        "3. 🔐 账号密码可能已过期或被修改",
        "4. 🌐 网络连接问题",
        "5. 🏠 设备不在同一网络环境中"
    ]
    
    log("💡 可能的失败原因:")
    for reason in failure_reasons:
        log(f"  {reason}")

def get_device_suggestions():
    """获取设备配置建议"""
    log("💡 设备配置建议:")
    
    suggestions = [
        "1. 📱 打开米家APP，查看小爱音箱的实际设备名称",
        "2. 🔍 尝试使用设备的DID (设备唯一标识符)",
        "3. 🏷️ 避免使用包含特殊字符的设备名称",
        "4. 📝 确保设备名称与米家APP中显示的完全一致",
        "5. 🔄 如果使用自定义名称，尝试改为默认名称"
    ]
    
    for suggestion in suggestions:
        log(f"  {suggestion}")

def check_network_connectivity():
    """检查网络连接"""
    log("🔍 检查网络连接...")
    
    test_urls = [
        "https://account.xiaomi.com",
        "https://api.mina.mi.com",
        "https://userprofile.mina.mi.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                log(f"✅ {url} - 连接正常")
            else:
                log(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            log(f"❌ {url} - 连接失败: {e}")

def generate_fix_recommendations():
    """生成修复建议"""
    log("🔧 修复建议:")
    
    recommendations = [
        "1. 🛡️ 立即完成小米账号验证:",
        "   python smart_verification_helper.py",
        "",
        "2. 📱 检查并修正设备ID配置:",
        "   - 打开米家APP查看设备真实名称",
        "   - 修改 .migpt.js 中的 did 配置",
        "   - 尝试使用英文名称或DID",
        "",
        "3. 🔄 重新配置容器:",
        "   docker stop xiaoai-llm",
        "   # 修改配置文件后",
        "   docker start xiaoai-llm",
        "",
        "4. 📊 监控容器状态:",
        "   python migpt_manager.py monitor",
        "",
        "5. 🧪 测试连接:",
        "   python test_xiaomi_connection.py"
    ]
    
    for rec in recommendations:
        log(f"  {rec}")

def main():
    print("🔍 小米账号和设备深度诊断")
    print("="*60)
    
    # 1. 检查容器配置
    check_container_config()
    print()
    
    # 2. 检查设备信息
    check_xiaomi_devices()
    print()
    
    # 3. 检查账号验证
    check_account_verification()
    print()
    
    # 4. 分析登录失败
    analyze_login_failure()
    print()
    
    # 5. 设备配置建议
    get_device_suggestions()
    print()
    
    # 6. 检查网络连接
    check_network_connectivity()
    print()
    
    # 7. 生成修复建议
    print("="*60)
    generate_fix_recommendations()
    
    print("\n" + "="*60)
    log("🎯 诊断完成！请按照建议进行修复")

if __name__ == "__main__":
    main()
