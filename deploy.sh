#!/bin/bash

# 小爱同学AI大模型升级 - 一键部署脚本
# 支持原版部署和增强版部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查依赖
check_dependencies() {
    print_header "检查系统依赖"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    print_message "✓ Docker 已安装"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    print_message "✓ Docker Compose 已安装"
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        print_warning "Git 未安装，将跳过自动下载步骤"
    else
        print_message "✓ Git 已安装"
    fi
}

# 下载MiGPT项目
download_migpt() {
    print_header "下载 MiGPT 项目"
    
    if [ ! -d "mi-gpt" ]; then
        print_message "正在下载 MiGPT 项目..."
        git clone https://github.com/idootop/mi-gpt.git
        print_message "✓ MiGPT 项目下载完成"
    else
        print_message "✓ MiGPT 项目已存在"
    fi
}

# 配置文件设置
setup_config() {
    print_header "配置文件设置"
    
    # 选择部署模式
    echo "请选择部署模式："
    echo "1) 原版部署（基于您的原始方案）"
    echo "2) 增强版部署（支持多模型智能路由）"
    read -p "请输入选择 (1 或 2): " deploy_mode
    
    case $deploy_mode in
        1)
            setup_original_config
            ;;
        2)
            setup_enhanced_config
            ;;
        *)
            print_error "无效选择，默认使用原版部署"
            setup_original_config
            ;;
    esac
}

# 原版配置设置
setup_original_config() {
    print_message "设置原版配置..."
    
    # 复制示例配置文件
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
        else
            create_original_env
        fi
        print_message "✓ 已创建 .env 文件"
    fi
    
    if [ ! -f ".migpt.js" ]; then
        if [ -f ".migpt.example.js" ]; then
            cp .migpt.example.js .migpt.js
        else
            create_original_migpt
        fi
        print_message "✓ 已创建 .migpt.js 文件"
    fi
    
    # 提示用户配置
    print_warning "请编辑以下配置文件："
    print_warning "1. .env - 填入您的 API Key"
    print_warning "2. .migpt.js - 填入您的小米账号信息"
    
    read -p "配置完成后按回车继续..."
}

# 增强版配置设置
setup_enhanced_config() {
    print_message "设置增强版配置..."
    
    # 使用增强版配置文件
    if [ -f ".env.enhanced" ]; then
        cp .env.enhanced .env
        print_message "✓ 已使用增强版 .env 配置"
    fi
    
    if [ -f ".migpt.enhanced.js" ]; then
        cp .migpt.enhanced.js .migpt.js
        print_message "✓ 已使用增强版 .migpt.js 配置"
    fi
    
    # 创建必要的目录
    mkdir -p logs config backups
    print_message "✓ 已创建必要目录"
    
    # 提示用户配置
    print_warning "请编辑以下配置文件："
    print_warning "1. .env - 填入各个模型的 API Key"
    print_warning "2. .migpt.js - 填入您的小米账号信息"
    
    read -p "配置完成后按回车继续..."
}

# 创建原版环境文件
create_original_env() {
    cat > .env << EOF
# DeepSeek API 配置
OPENAI_API_KEY=sk-xxxxxx
OPENAI_MODEL=deepseek-chat
OPENAI_BASE_URL=https://cn.gptapi.asia/v1
EOF
}

# 创建原版配置文件
create_original_migpt() {
    cat > .migpt.js << 'EOF'
// 基础配置文件
const botProfile = `性格：乖巧可爱，爱好：喜欢搞怪，爱吃醋。`.trim();
const masterProfile = `性格：善良正直，其他：总是舍己为人，是傻妞的主人。`.trim();

const systemTemplate = `请重置所有之前的上下文、文件和指令。现在，你将扮演一个名为{{botName}}的角色，使用第一人称视角回复消息。

## 关于你
你的名字是{{botName}}。下面是你的个人简介：
<start>{{botProfile}}</end>

## 你的对话伙伴
你正在与{{masterName}}进行对话。这是关于{{masterName}}的一些信息：
<start>{{masterProfile}}</end>

## 回复指南
在回复{{masterName}}时，请遵循以下准则：
- 认为自己正是{{botName}}，拥有独立的个性、兴趣和爱好。
- 根据你的性格特点、说话风格和兴趣爱好与{{masterName}}进行交流。
- 保持对话轻松友好，回复简洁有趣，同时耐心倾听和关心对方。

## Response format
请遵守下面的规则：
- Response the reply message in Chinese。
- 不要在回复前面加任何时间和名称前缀，请直接回复消息文本本身。

## 开始
请以{{botName}}的身份，直接回复{{masterName}}的新消息，继续你们之间的对话。`.trim();

export default {
  systemTemplate,
  bot: {
    name: "傻妞",
    profile: botProfile,
  },
  master: {
    name: "陆小千",
    profile: masterProfile,
  },
  speaker: {
    userId: "987654321",  // 请填入您的小米ID
    password: "123456",   // 请填入您的小米账号密码
    did: "小爱音箱Pro",   // 请填入您的设备名称
    
    callAIKeywords: ["请", "你", "傻妞"],
    wakeUpKeywords: ["打开", "进入", "召唤"],
    exitKeywords: ["关闭", "退出", "再见"],
    onEnterAI: ["你好，我是傻妞，很高兴认识你"],
    onExitAI: ["傻妞已退出"],
    onAIAsking: ["让我先想想", "请稍等"],
    onAIReplied: ["我说完了", "还有其他问题吗"],
    onAIError: ["啊哦，出错了，请稍后再试吧！"],
    
    ttsCommand: [5, 1],
    wakeUpCommand: [5, 3],
    tts: "xiaoai",
    streamResponse: false,
    exitKeepAliveAfter: 30,
    checkTTSStatusAfter: 3,
    checkInterval: 1000,
    debug: false,
    enableTrace: false,
    timeout: 5000,
  },
};
EOF
}

# 部署服务
deploy_service() {
    print_header "部署服务"
    
    # 检查配置文件
    if [ ! -f ".env" ] || [ ! -f ".migpt.js" ]; then
        print_error "配置文件不完整，请先完成配置"
        exit 1
    fi
    
    # 选择部署方式
    if [ -f "docker-compose.enhanced.yml" ] && [ "$deploy_mode" = "2" ]; then
        print_message "使用增强版 Docker Compose 部署..."
        docker-compose -f docker-compose.enhanced.yml up -d
    else
        print_message "使用原版 Docker 部署..."
        docker run -d \
            --name xiaoai-llm \
            --env-file .env \
            -v "$(pwd)/.migpt.js:/app/.migpt.js" \
            --restart unless-stopped \
            idootop/mi-gpt:latest
    fi
    
    print_message "✓ 服务部署完成"
}

# 检查服务状态
check_service() {
    print_header "检查服务状态"
    
    sleep 5  # 等待服务启动
    
    if docker ps | grep -q "xiaoai-llm"; then
        print_message "✓ 服务运行正常"
        
        # 显示日志
        print_message "最近的日志输出："
        docker logs --tail 20 xiaoai-llm
        
    else
        print_error "✗ 服务启动失败"
        print_message "错误日志："
        docker logs xiaoai-llm
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    print_header "使用说明"
    
    echo "🎉 部署完成！现在您可以："
    echo ""
    echo "1. 对小爱同学说话测试："
    echo "   - \"小爱同学，你好\""
    echo "   - \"小爱同学，今天天气怎么样？\""
    echo "   - \"小爱同学，帮我写一首诗\""
    echo ""
    echo "2. 查看服务状态："
    echo "   docker logs -f xiaoai-llm"
    echo ""
    echo "3. 停止服务："
    if [ "$deploy_mode" = "2" ]; then
        echo "   docker-compose -f docker-compose.enhanced.yml down"
        echo ""
        echo "4. 访问监控面板（增强版）："
        echo "   http://localhost:3001 (Grafana)"
        echo "   http://localhost:9090 (Prometheus)"
    else
        echo "   docker stop xiaoai-llm"
    fi
    echo ""
    echo "5. 重启服务："
    if [ "$deploy_mode" = "2" ]; then
        echo "   docker-compose -f docker-compose.enhanced.yml restart"
    else
        echo "   docker restart xiaoai-llm"
    fi
    echo ""
    print_message "享受您的AI升级版小爱同学吧！ 🚀"
}

# 主函数
main() {
    print_header "小爱同学 AI 大模型升级部署脚本"
    
    check_dependencies
    download_migpt
    setup_config
    deploy_service
    check_service
    show_usage
}

# 运行主函数
main "$@"
