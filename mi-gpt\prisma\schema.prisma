// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:app.db"
}

model User {
  id                String            @id @default(uuid())
  name              String
  profile           String
  // 关联数据
  rooms             Room[]            @relation("RoomMembers")
  messages          Message[]
  memories          Memory[]
  shortTermMemories ShortTermMemory[]
  longTermMemories  LongTermMemory[]
  // 时间日期
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
}

model Room {
  id                String            @id @default(uuid())
  name              String
  description       String
  // 关联数据
  members           User[]            @relation("RoomMembers")
  messages          Message[]
  memories          Memory[]
  shortTermMemories ShortTermMemory[]
  longTermMemories  LongTermMemory[]
  // 时间日期
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
}

model Message {
  id        Int      @id @default(autoincrement())
  text      String
  // 关联数据
  sender    User     @relation(fields: [senderId], references: [id])
  senderId  String
  room      Room     @relation(fields: [roomId], references: [id])
  roomId    String
  memories  Memory[]
  // 时间日期
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Memory {
  id                Int               @id @default(autoincrement())
  // 关联数据
  msg               Message           @relation(fields: [msgId], references: [id])
  msgId             Int
  owner             User?             @relation(fields: [ownerId], references: [id]) // owner 为空时，即房间自己的公共记忆
  ownerId           String?
  room              Room              @relation(fields: [roomId], references: [id])
  roomId            String
  shortTermMemories ShortTermMemory[]
  // 时间日期
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
}

model ShortTermMemory {
  id               Int              @id @default(autoincrement())
  text             String
  // 关联数据
  cursor           Memory           @relation(fields: [cursorId], references: [id]) // 记忆最后更新的位置
  cursorId         Int
  owner            User?            @relation(fields: [ownerId], references: [id]) // owner 为空时，即房间自己的公共记忆
  ownerId          String?
  room             Room             @relation(fields: [roomId], references: [id])
  roomId           String
  longTermMemories LongTermMemory[]
  // 时间日期
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
}

model LongTermMemory {
  id        Int             @id @default(autoincrement())
  text      String
  // 关联数据
  cursor    ShortTermMemory @relation(fields: [cursorId], references: [id])
  cursorId  Int
  owner     User?           @relation(fields: [ownerId], references: [id]) // owner 为空时，即房间自己的公共记忆
  ownerId   String?
  room      Room            @relation(fields: [roomId], references: [id])
  roomId    String
  // 时间日期
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
}
